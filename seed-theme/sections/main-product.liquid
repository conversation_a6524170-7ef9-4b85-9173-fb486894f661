{%- liquid
    assign product_image_ratio = settings.product_image_ratio
    case product_image_ratio
        when '310x430'
            assign image_ratio =  "portrait"
            assign image_width =  310
            assign image_height = 430
        when '430x310'
            assign image_ratio =  "landscape"
            assign image_width =  430
            assign image_height = 310
        else
            assign image_ratio =  "square"
            assign image_width =  430
            assign image_height = 430
    endcase

    assign image_width_small = image_width | divided_by: 3
    assign image_height_small = image_height | divided_by: 3

    assign current_variant = product.selected_or_first_available_variant
    assign featured_media_position = current_variant.featured_media.position
    unless featured_media_position
        assign featured_media_position = 0
    endunless
    assign variant_selection_block = section.blocks | where: "type", "variant_selection"
    assign title_block = section.blocks | where: "type", "title" | first
    assign buy_button_block = section.blocks | where: "type", "buy_button" | first
    assign first_block = section.blocks | first
    assign share_whatsapp = title_block.settings.share_whatsapp
    assign share_facebook = title_block.settings.share_facebook
    assign share_twitter = title_block.settings.share_twitter
    assign share_pinterest = title_block.settings.share_pinterest
    assign share_messenger = title_block.settings.share_messenger
    assign share_email = title_block.settings.share_email
    assign sharing_product = product.current_variant | default: product

    assign and_translation = ' and '
    assign has_pros_and_cons = false
    assign pros_split_char = '|'
    assign cons_split_char = '|'
    assign specs_split_char = '|'
    unless section.settings.pros contains 'metafield-single_line_text_field-array'
        assign pros_split_char = ','
    endunless
    unless section.settings.cons contains 'metafield-single_line_text_field-array'
        assign cons_split_char = ','
    endunless
    assign pros = section.settings.pros | remove: '<ul class="metafield-single_line_text_field-array">' | remove: '<li class="metafield-single_line_text_field">' | replace: '</li>', pros_split_char | remove: '</ul>' | remove: '<p>' | remove: '</p>' | newline_to_br | strip_newlines | replace: '<br/>', pros_split_char | replace: '<br />', pros_split_char | replace: and_translation, pros_split_char | split: pros_split_char
    assign cons = section.settings.cons | remove: '<ul class="metafield-single_line_text_field-array">' | remove: '<li class="metafield-single_line_text_field">' | replace: '</li>', cons_split_char | remove: '</ul>' | remove: '<p>' | remove: '</p>' | newline_to_br | strip_newlines | replace: '<br/>', cons_split_char | replace: '<br />', cons_split_char | replace: and_translation, cons_split_char | split: cons_split_char
    if pros.size > 0 or cons.size > 0
        assign has_pros_and_cons = true
    endif
    assign has_specs = false
    unless section.settings.specifications contains 'metafield-single_line_text_field-array'
        assign specs_split_char = ','
    endunless
    assign specs = section.settings.specifications | remove: '<ul class="metafield-single_line_text_field-array">' | remove: '<li class="metafield-single_line_text_field">' | replace: '</li>', specs_split_char | remove: '</ul>' | remove: '<p>' | remove: '</p>' | newline_to_br | strip_newlines | replace: '<br/>', specs_split_char | replace: '<br />', specs_split_char | replace: and_translation, specs_split_char | split: specs_split_char
    if specs.size > 0
        assign has_specs = true
    endif

    assign main_width = 700 | minus: 38 | minus: 38
    if section.settings.images_layout == 'aside'
        assign main_width = main_width | minus: 54
    endif
    assign sizes = '(min-width: 1000px) ' | append: main_width | append: 'px, 100vw'

    assign product_title = product.title
    if title_block.settings.title != blank
      assign product_title = title_block.settings.title
    endif

    assign preorder = false
    if current_variant.inventory_management != nil and current_variant.inventory_policy == 'continue' and current_variant.available and current_variant.inventory_quantity <= 0 and buy_button_block.settings.preorder and product.gift_card? == false
        assign preorder = true
    endif

    assign form_id = 'main-product-form-' | append: product.id

    assign rating_value = false
    assign rating_count = false
    if product.metafields.syncer.reviews
        assign locale_ratings = product.metafields.syncer.reviews.value.reviews[localization.language.iso_code]
        if locale_ratings
            assign rating_value = locale_ratings.rating
            assign rating_count = locale_ratings.count
        else
            assign rating_value = product.metafields.syncer.reviews.value.cumulative_rating
            assign rating_count = product.metafields.syncer.reviews.value.total_reviews
        endif
    elsif product.metafields.reviews.rating
        assign rating_value = product.metafields.reviews.rating.value
        assign rating_count = product.metafields.reviews.rating_count
    endif
-%}

{%- comment -%} Klarna Payment Styles {%- endcomment -%}
<link href="{{ 'klarna-payment.css' | asset_url }}" rel="stylesheet">

{%- capture header -%}
    <header {{ title_block.shopify_attributes }} class="class-x">
        <{{ title_block.settings.title_size }} class="m5{% if settings.product_titles_caps %} text-uppercase{% endif %}">{{ product_title }}</{{ title_block.settings.title_size }}>
        <ul class="l4dr m15 base-font">
            {%- if rating_value and title_block.settings.show_product_rating -%}
                <li>
                    <a href="#section-reviews" class="r6rt" data-val="{{ rating_value }}" data-of="5">
                        {%- if rating_count -%}{{ rating_count }} <span>{{ 'product.reviews.count' | t: count: rating_count }}</span>{%- endif -%}
                    </a>
                </li>
            {%- endif -%}
            {%- if title_block.settings.show_vendor_brand or title_block.settings.show_vendor_name -%}
                {%- if product.vendor != "vendor-unknown" and product.vendor != shop.name -%}
                    <li>{%- if title_block.settings.show_vendor_brand %}<span class="strong">{{ 'product.vendor' | t }}</span>{%- endif -%}
                        {%- liquid
                            assign vendor_handle = product.vendor | handleize
                            assign vendor_collection = collections[vendor_handle]
                        -%}
                        {%- if vendor_collection != blank and title_block.settings.show_vendor_name -%}<a href="{{ vendor_collection.url }}">{{ product.vendor }}</a>{%- elsif title_block.settings.show_vendor_name -%}{{ product.vendor }}{%- endif -%}
                    </li>
                {%- endif -%}
            {%- endif -%}
            {%- if share_whatsapp or share_facebook or share_twitter or share_pinterest or share_messenger or share_email -%}
                <li class="has-social">
                    <a href="./" class="toggle" aria-label="{{ 'social_share.share_this_product' | t }}"><i aria-hidden="true" class="icon-share"></i> <span class="mobile-hide">{{ 'social_share.share_this_product' | t }}</span></a>
                    <ul class="l4sc box">
                        {%- render 'social-share-buttons', share: sharing_product, share_whatsapp: share_whatsapp, share_facebook: share_facebook, share_twitter: share_twitter, share_pinterest: share_pinterest, share_messenger: share_messenger, share_email: share_email -%}
                    </ul>
                </li>
            {%- endif -%}
        </ul>
    </header>
{%- endcapture -%}

<article id="main-product" class="m6pr m6pr-{{ section.id }}" data-template="{{ section.id }}" data-form-id="{{ form_id }}" data-product-url="{{ product.url }}">
    {%- comment -%} Hide original header since we're showing it in right panel {%- endcomment -%}
    {%- if title_block.settings.layout != 'title-right' -%}
        {%- if title_block.settings.layout == 'title-right' and title_block.settings.layout_mobile == 'title-top' -%}
            {{ header | replace: 'class-x', 'mobile-only' }}
        {%- elsif title_block.settings.layout == 'title-top' and title_block.settings.layout_mobile == 'title-bottom' -%}
            {{ header | replace: 'class-x', 'mobile-hide' }}
        {%- elsif title_block.settings.layout == 'title-top' and title_block.settings.layout_mobile == 'title-top' %}
            {{ header | replace: 'class-x', '' }}
        {%- endif -%}
    {%- endif -%}
    <div class="l4pr-container">
        <ul class="l4pr
      {% if section.settings.show_thumbs_desktop == false %}no-thumbs-desktop{% endif %}{% if section.settings.show_thumbs_mobile == false %} no-thumbs-mobile strong{% endif %}
      {% if section.settings.images_layout == 'aside' and section.settings.show_thumbs_desktop %}aside-pager{% endif %}
      no-scrollbar
      "
            data-featured_media_position={{ featured_media_position }}>
            {%- if product.media.size == 0 -%}
                <li>
                    {%- render 'product-labels' -%}
                    <picture class="{% if settings.multiply_product_images == 'multiply' %}img-multiply{% elsif settings.multiply_product_images == 'multiply-bg' %}img-multiply-bg{% endif %}">
                        {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}
                    </picture>
                </li>
            {%- endif -%}
            {%- for media in product.media -%}
                <li class="{% if settings.fill_product_images %} cover{% endif %}">
                    <a data-fancybox="product-gallery-{{ product.id }}"
                            {% if media.media_type == 'image' %}
                                href="{{ media | image_url }}"
                            {% elsif media.media_type == 'external_video' %}
                                href="{{ media | external_video_url }}"
                            {% elsif media.media_type == 'video' %}
                                {%- liquid
                                    assign source = media.sources | where: "height", 1080 | where: "format", "mp4" | first
                                    if source == nil
                                        assign source = media.sources | where: "format", "mp4" | first
                                    endif
                                    if source == nil
                                        assign source = media.sources | where: "format", "mov" | first
                                    endif
                                    if source == nil
                                        assign source = media.sources.first
                                    endif
                                -%}
                                href="{{ source.url }}"
                            {% elsif media.media_type == 'model' %}
                                href="#model-3d-{{ forloop.index }}"
                            {% endif %}
                       data-gallery-thumb="
              {% if settings.fill_product_images %}
                {{ media.preview_image | image_url: width: image_width_small, height: image_height_small, crop: 'center' }}
              {% else %}
                {{ media.preview_image | image_url: width: image_width_small }}
              {% endif %}
            "
                    >
                        {%- if forloop.first -%}
                            {%- render 'product-labels' -%}
                        {%- endif -%}
                        <picture class="{% if settings.multiply_product_images == 'multiply' %}img-multiply{% elsif settings.multiply_product_images == 'multiply-bg' %}img-multiply-bg{% endif %}">
                            {%- liquid
                                assign lazyload = true
                                if forloop.index == featured_media_position
                                    assign lazyload = false
                                elsif forloop.first and featured_media_position == 0
                                    assign lazyload = false
                                endif
                            -%}
                            {% if media.image %}
                                {% capture srcset %}
                                    {%- liquid
                                        if settings.fill_product_images
                                            render 'image-srcset', image: media, format: image_ratio, crop: 'center'
                                        else
                                            render 'image-srcset', image: media, format: image_ratio
                                        endif
                                    -%}
                                {% endcapture %}
                                {%- if lazyload -%}
                                    {{ media | image_url: width: image_width, height: image_height, crop: 'center' | image_tag: srcset: srcset, sizes: sizes, loading: 'lazy' }}
                                {%- else -%}
                                    {{ media | image_url: width: image_width, height: image_height, crop: 'center' | image_tag: srcset: srcset, sizes: sizes, preload: true }}
                                {%- endif -%}
                            {%- else -%}
                                {%- if lazyload -%}
                                    {{ media.preview_image | image_url: width: image_width, height: image_height, crop: 'center' | image_tag: sizes: sizes, loading: 'lazy' }}
                                {%- else -%}
                                    {{ media.preview_image | image_url: width: image_width, height: image_height, crop: 'center' | image_tag: sizes: sizes, preload: true }}
                                {%- endif -%}
                            {%- endif -%}
                            {%- if media.media_type == 'external_video' or media.media_type == 'video' -%}
                                <i aria-hidden="true" class="icon-play"></i>
                            {%- elsif media.media_type == 'model' -%}
                                <span id="model-3d-{{ forloop.index }}" class="model-3d">
                  {{ media | model_viewer_tag: shadow-intensity: "1" }}
                  <button
                          data-shopify-xr
                          data-shopify-model3d-id="{{ media.id }}"
                          data-shopify-title="{{ product_title | escape }}"
                          data-shopify-xr-hidden
                  ><i aria-hidden="true" class="icon-cube"></i> {{ 'product.view_in_your_space' | t }}</button>
                </span>
                                <i aria-hidden="true" class="icon-cube"></i>
                            {%- endif -%}
                        </picture>
                    </a>
                    {%- if media.media_type == 'model' -%}
                        <button
                                data-shopify-xr
                                data-shopify-model3d-id="{{ media.id }}"
                                data-shopify-title="{{ product_title | escape }}"
                                data-shopify-xr-hidden
                        ><i aria-hidden="true" class="icon-cube"></i> {{ 'product.view_in_your_space' | t }}</button>
                    {%- endif -%}
                </li>
            {%- endfor -%}
        </ul>
        {%- if has_pros_and_cons == false and has_specs == false -%}
            {%- if section.settings.show_product_description and product.description != empty -%}
                <footer class="m6tb static desktop-only base-font">
                    <nav class="hidden">
                        <ul>
                            <li class="active"><a href="#section-info">{{ 'product.description' | t }}</a></li>
                        </ul>
                    </nav>
                    <div>
                        <div id="section-info">
                            <{{ section.settings.description_title_size }} class="mobile-hide">{{ 'product.description' | t }}</{{ section.settings.description_title_size }}>
                            {%- if section.settings.product_description_enable_read_more -%}
                                <div class="m6lm">
                                    {{ product.description }}
                                </div>
                                <p class="has-link-more"><a href="./" class="strong link-more">{{ 'general.read_more.read' | t }} <span>{{ 'general.read_more.more' | t }}</span> <span class="hidden">{{ 'general.read_more.less' | t }}</span> <i aria-hidden="true" class="icon-chevron-down"></i></a></p>
                            {%- else -%}
                                {{ product.description }}
                            {%- endif -%}
                        </div>
                    </div>
                </footer>
            {%- endif %}
        {%- endif %}
    </div>
    {%- assign has_model = product.media | where: "media_type", "model" | first -%}
    {%- if has_model -%}
        <script type="application/json" id="ProductJSON-{{ product.id }}">
        {{ product.media | where: 'media_type', 'model' | json }}
      </script>
    {%- endif -%}

    {%- comment -%} Product variants data for price updates {%- endcomment -%}
    <script type="application/json" id="ProductVariantsJSON-{{ product.id }}">
      {
        "variants": {{ product.variants | json }}
      }
    </script>
    <div>
        {%- liquid
            # Hide original header logic since we show it in right panel
            assign form_class = 'f8pr f8pr-buy-button'
        -%}
        <div class="f8pr base-font{% if current_variant == null %} unavailable{% endif %}">
            <legend>{{ 'product.form.title' | t }}</legend>

            {%- comment -%} Custom breadcrumbs and title in right panel {%- endcomment -%}
            <div class="product-right-header">
                {%- comment -%} Breadcrumbs {%- endcomment -%}
                <nav class="product-breadcrumbs n6br{% if settings.breadcrumbs_font == 'primary' %} title-font{% endif %}" role="navigation" aria-label="breadcrumbs">
                    <ol>
                        <li>
                            <a href="{{ routes.root_url }}" title="Home">{{ 'general.breadcrumbs.home' | t }}</a>
                        </li>
                        {%- if product.collections.first -%}
                            <li>
                                <a href="{{ product.collections.first.url }}">{{ product.collections.first.title }}</a>
                            </li>
                        {%- endif -%}
                        <li>
                            {{ product.title }}
                        </li>
                    </ol>
                </nav>

                {%- comment -%} Product title {%- endcomment -%}
                <{{ title_block.settings.title_size }} class="product-right-title m5{% if settings.product_titles_caps %} text-uppercase{% endif %}">{{ product.title }}</{{ title_block.settings.title_size }}>

                {%- comment -%} Custom price section {%- endcomment -%}
                {%- assign current_variant = product.selected_or_first_available_variant -%}
                {%- if current_variant -%}
                    <div class="product-right-price" id="product-right-price-{{ section.id }}">
                        {%- if current_variant.compare_at_price > current_variant.price -%}
                            <span class="price-original" data-compare-price>${{ current_variant.compare_at_price | money_without_currency }}</span>
                            <span class="price-sale" data-price>SALE ${{ current_variant.price | money_without_currency }}</span>
                            {%- if title_block.settings.price_suffix != blank -%}
                                <span class="price-suffix">{{ title_block.settings.price_suffix }}</span>
                            {%- endif -%}
                        {%- else -%}
                            <span class="price-regular" data-price>${{ current_variant.price | money_without_currency }}</span>
                        {%- endif -%}
                    </div>
                {%- endif -%}

                {%- comment -%} Klarna Payment Info {%- endcomment -%}
                {%- render 'klarna-payment-info', product: product, section_id: section.id -%}

                {%- comment -%} Product rating and additional info {%- endcomment -%}
                <ul class="l4dr m15 base-font product-right-meta">
                    {%- if rating_value and title_block.settings.show_product_rating -%}
                        <li>
                            <a href="#section-reviews" class="r6rt" data-val="{{ rating_value }}" data-of="5">
                                {%- if rating_count -%}{{ rating_count }} <span>{{ 'product.reviews.count' | t: count: rating_count }}</span>{%- endif -%}
                            </a>
                        </li>
                    {%- endif -%}
                    {%- if title_block.settings.show_vendor_brand or title_block.settings.show_vendor_name -%}
                        {%- if product.vendor != "vendor-unknown" and product.vendor != shop.name -%}
                            <li>{%- if title_block.settings.show_vendor_brand %}<span class="strong">{{ 'product.vendor' | t }}</span>{%- endif -%}
                                {%- liquid
                                    assign vendor_handle = product.vendor | handleize
                                    assign vendor_collection = collections[vendor_handle]
                                -%}
                                {%- if vendor_collection != blank and title_block.settings.show_vendor_name -%}<a href="{{ vendor_collection.url }}">{{ product.vendor }}</a>{%- elsif title_block.settings.show_vendor_name -%}{{ product.vendor }}{%- endif -%}
                            </li>
                        {%- endif -%}
                    {%- endif -%}
                </ul>
            </div>

            {%- for block in section.blocks -%}
                {%- case block.type -%}
                {%- when '@app' -%}
                    {% render block %}
                {%- when 'spacer' -%}
                    <div class="module-spacer" style="margin-bottom:{{ block.settings.height }}px;" {{ block.shopify_attributes }}></div>
                {%- when 'custom_liquid' -%}
                    {{ block.settings.custom_liquid }}
                {%- when 'custom_html' -%}
                    {{ block.settings.custom_html }}
                {%- when 'title' -%}
                    {%- comment -%} Title is now shown in right panel, skip original display {%- endcomment -%}
                {%- when 'text' -%}
                    <span {{ block.shopify_attributes }}>{{ block.settings.text }}</span>
                {%- when 'short_description' -%}
                  {%- if settings.product_short_description != 'none' %}
                      {%- liquid
                          if settings.product_short_description == 'custom'
                            assign product_short_description_namespace = settings.product_short_description_text | split: '.' | first
                            assign product_short_description_key = settings.product_short_description_text | split: '.' | last
                            assign short_description = product.metafields[product_short_description_namespace][product_short_description_key] | strip_html
                            assign custom_short_description = true
                          else
                            assign short_description = product.description | strip_html | truncatewords: 20
                          endif
                      -%}
                      {%- if short_description != empty and short_description != nil -%}
                        <p class="m15 mobile-hide" {{ block.shopify_attributes }}>
                            {{ short_description }}
                            {%- if block.settings.show_read_more -%}
                                <a href="#section-info" class="strong">{{ 'general.read_more.read_more' | t }}</a>
                            {%- endif -%}
                        </p>
                      {%- endif -%}
                  {%- endif -%}
                {%- when 'custom_color_selection' -%}
                    <div class="custom-color-selection" {{ block.shopify_attributes }}>
                        {%- liquid
                            assign color_option = null
                            assign triggers = settings.color_swatch_name | newline_to_br | strip_newlines | replace: '<br />', '|' | split: '|'

                            if settings.enable_color_swatches
                                for option in product.options_with_values
                                    if triggers contains option.name
                                        assign color_option = option
                                        break
                                    endif
                                endfor
                            else
                                # Fallback: try to find any color-like option
                                for option in product.options_with_values
                                    assign option_name_lower = option.name | downcase
                                    if option_name_lower contains 'color' or option_name_lower contains 'colour' or option_name_lower contains 'цвет'
                                        assign color_option = option
                                        break
                                    endif
                                endfor
                            endif
                        -%}

                        {%- comment -%} Test version - always show some colors {%- endcomment -%}
                        <div class="custom-color-wrapper">
                            <label class="custom-color-label">
                                {{ block.settings.color_label | default: 'Color' }}:
                                <span class="custom-color-selected" data-selected-color>
                                    {%- if color_option -%}{{ color_option.selected_value }}{%- else -%}blue{%- endif -%}
                                </span>
                            </label>

                            <div class="custom-color-options">
                                {%- if color_option and color_option.values.size > 0 -%}
                                    {%- comment -%} Real product colors {%- endcomment -%}
                                    {%- for color_value in color_option.values -%}
                                        {%- liquid
                                            assign color_variant = null
                                            # Find the first available variant for this color
                                            for variant in product.variants
                                                assign variant_matches = false
                                                if variant.option1 == color_value or variant.option2 == color_value or variant.option3 == color_value
                                                    assign variant_matches = true
                                                endif

                                                if variant_matches
                                                    if color_variant == null
                                                        assign color_variant = variant
                                                    elsif variant.available and color_variant.available == false
                                                        # Prefer available variants
                                                        assign color_variant = variant
                                                    endif
                                                endif
                                            endfor
                                        -%}

                                        <div class="custom-color-option">
                                            <input
                                                type="radio"
                                                id="custom-color-{{ section.id }}-{{ forloop.index }}"
                                                name="custom-color-{{ section.id }}"
                                                value="{{ color_value | escape }}"
                                                data-color-name="{{ color_value }}"
                                                {% if color_variant %}
                                                    data-variant-id="{{ color_variant.id }}"
                                                    data-l4pr-index="{{ color_variant.featured_media.position | minus: 1 }}"
                                                    data-template="{{ section.id }}"
                                                {% endif %}
                                                {% if color_value == color_option.selected_value %}checked{% endif %}
                                                {% unless color_variant.available %}disabled{% endunless %}
                                                class="custom-color-input"
                                            >
                                            <label
                                                for="custom-color-{{ section.id }}-{{ forloop.index }}"
                                                class="custom-color-swatch"
                                                {% unless color_variant.available %}
                                                    title="Out of stock"
                                                    data-tooltip="Out of stock"
                                                {% endunless %}
                                            >
                                                {%- if color_value.swatch.image -%}
                                                    <span class="custom-color-circle" style="background-image: url('{{ color_value.swatch.image | image_url }}');"></span>
                                                {%- elsif color_value.swatch.color -%}
                                                    <span class="custom-color-circle" style="background-color: {{ color_value.swatch.color }};"></span>
                                                {%- else -%}
                                                    <span class="custom-color-circle swatch-custom-color-{{ color_value | handleize }}" style="background-color: {{ color_value | split: ' ' | last }};"></span>
                                                {%- endif -%}
                                            </label>
                                        </div>
                                    {%- endfor -%}
                                {%- else -%}
                                    {%- comment -%} Demo colors for testing {%- endcomment -%}
                                    <div class="custom-color-option">
                                        <input type="radio" id="custom-color-{{ section.id }}-demo1" name="custom-color-{{ section.id }}" value="blue" data-color-name="blue" checked class="custom-color-input">
                                        <label for="custom-color-{{ section.id }}-demo1" class="custom-color-swatch">
                                            <span class="custom-color-circle" style="background-color: #0066cc;"></span>
                                        </label>
                                    </div>
                                    <div class="custom-color-option">
                                        <input type="radio" id="custom-color-{{ section.id }}-demo2" name="custom-color-{{ section.id }}" value="orange" data-color-name="orange" class="custom-color-input">
                                        <label for="custom-color-{{ section.id }}-demo2" class="custom-color-swatch">
                                            <span class="custom-color-circle" style="background-color: #ff6600;"></span>
                                        </label>
                                    </div>
                                    <div class="custom-color-option">
                                        <input type="radio" id="custom-color-{{ section.id }}-demo3" name="custom-color-{{ section.id }}" value="black" data-color-name="black" disabled class="custom-color-input">
                                        <label for="custom-color-{{ section.id }}-demo3" class="custom-color-swatch" title="Out of stock" data-tooltip="Out of stock">
                                            <span class="custom-color-circle" style="background-color: #000000;"></span>
                                        </label>
                                    </div>
                                    <div class="custom-color-option">
                                        <input type="radio" id="custom-color-{{ section.id }}-demo4" name="custom-color-{{ section.id }}" value="beige" data-color-name="beige" class="custom-color-input">
                                        <label for="custom-color-{{ section.id }}-demo4" class="custom-color-swatch">
                                            <span class="custom-color-circle" style="background-color: #d4b896;"></span>
                                        </label>
                                    </div>
                                {%- endif -%}
                            </div>
                        </div>
                    </div>
                {%- when 'promo_banner' -%}
                    <div class="promo-banner-block" {{ block.shopify_attributes }}>
                        <div class="promo-banner-container"
                             style="background-color: {{ block.settings.promo_bg_color | default: '#f8f9fa' }}; color: {{ block.settings.promo_text_color | default: '#6c757d' }};">

                            {%- if block.settings.promo_link != blank -%}
                                <a href="{{ block.settings.promo_link }}" class="promo-banner-link">
                            {%- endif -%}

                            <div class="promo-banner-content">
                                {%- if block.settings.promo_icon != 'none' -%}
                                    <div class="promo-banner-icon">
                                        {%- case block.settings.promo_icon -%}
                                            {%- when 'tag' -%}
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/>
                                                    <line x1="7" y1="7" x2="7.01" y2="7"/>
                                                </svg>
                                            {%- when 'percent' -%}
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <line x1="19" y1="5" x2="5" y2="19"/>
                                                    <circle cx="6.5" cy="6.5" r="2.5"/>
                                                    <circle cx="17.5" cy="17.5" r="2.5"/>
                                                </svg>
                                            {%- when 'gift' -%}
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <polyline points="20,12 20,22 4,22 4,12"/>
                                                    <rect x="2" y="7" width="20" height="5"/>
                                                    <line x1="12" y1="22" x2="12" y2="7"/>
                                                    <path d="M12,7H7.5a2.5,2.5 0 0,1 0,-5C11,2 12,7 12,7z"/>
                                                    <path d="M12,7h4.5a2.5,2.5 0 0,0 0,-5C13,2 12,7 12,7z"/>
                                                </svg>
                                        {%- endcase -%}
                                    </div>
                                {%- endif -%}

                                <div class="promo-banner-text">
                                    {%- if block.settings.promo_title != blank -%}
                                        <div class="promo-banner-title">{{ block.settings.promo_title }}</div>
                                    {%- endif -%}
                                    {%- if block.settings.promo_subtitle != blank -%}
                                        <div class="promo-banner-subtitle">{{ block.settings.promo_subtitle }}</div>
                                    {%- endif -%}
                                    {%- if block.settings.promo_additional != blank -%}
                                        <div class="promo-banner-additional">{{ block.settings.promo_additional }}</div>
                                    {%- endif -%}
                                </div>
                            </div>

                            {%- if block.settings.promo_link != blank -%}
                                </a>
                            {%- endif -%}
                        </div>
                    </div>
                {%- when 'variant_selection' -%}
                    {%- liquid
                        assign triggers = settings.color_swatch_name | newline_to_br | strip_newlines | replace: '<br />', '|' | split: '|'
                        assign pick_up_availabilities = current_variant.store_availabilities | where: 'pick_up_enabled', true
                        assign pickup_available = false
                        if pick_up_availabilities.size > 0 and preorder == false
                            for pick_up_location in pick_up_availabilities
                                if pick_up_location.available
                                    assign pickup_available = true
                                    break
                                endif
                            endfor
                        endif
                    -%}
                    <div class="f8pr-variant-selection no-zindex" data-current-variant="{{ current_variant.id }}" {{ block.shopify_attributes }}>
                        {%- liquid
                            assign is_combined_listing = false
                            if product.options_with_values.first.values.first.product_url
                                assign is_combined_listing = true
                            endif
                            assign one_variant = false
                            if is_combined_listing == false and product.variants.size == 1
                                assign one_variant = true
                            endif
                        -%}
                        {%- if product.variants.size > 1 or block.settings.show_single_options or is_combined_listing or pickup_available -%}
                            {%- liquid
                                assign enable_options = false
                                assign is_color = false
                                if block.settings.options or product.options.size == 1 or is_combined_listing or product.variants.size == 250
                                    if product.options.size > 1
                                        assign enable_options = true
                                    endif
                                    for option in product.options_with_values
                                        if settings.enable_color_swatches and triggers contains option.name
                                            assign is_color = true
                                        endif 
                                    endfor
                                endif
                            -%}
                            {% if block.settings.selection_type == 'dropdown' %}<p {% if enable_options %}class="js-hidden"{% endif %}>{% endif %}
                            <label {% if block.settings.selection_type == 'dropdown' %}for="variant-id"{% endif %}{% if enable_options %} class="js-hidden"{% endif %}>
                                {%- if product.has_only_default_variant == false and product.variants.size > 1 -%}
                                    {{ product.options.first }}{% if is_color and block.settings.selection_type == 'buttons' %}<span class="data-change-to-{{ section.id }}">{{ current_variant.title }}</span>{% endif %}
                                {%- endif -%}
                                <span id="f8pr-pickup-1" class="f8pr-pickup pickup{% if product.variants.size > 1 %} text-end{% endif %}{% unless pickup_available %} hidden{% endunless %}"><span class="mobile-hide"><i aria-hidden="true" class="icon-pin"></i>{{ 'product.pickup_availability.free_pickup' | t }} <a href="#pickup-availability" data-pickup-availability data-id="{{ current_variant.id }}">{{ 'product.pickup_availability.our_stores' | t }} </a></span></span>
                            </label>
                            {%- if one_variant == false or block.settings.show_single_options and product.has_only_default_variant == false -%}
                                {%- liquid
                                    assign variants_with_images = product.variants | where: "image"
                                    assign show_images = true
                                    if is_color
                                        if product.variants.size != variants_with_images.size or block.settings.show_variant_images == false
                                            assign show_images = false
                                        endif
                                    endif
                                -%}
                                {%- if block.settings.selection_type == 'dropdown' -%}
                                    <select name="variant-id" id="id-{{ section.id }}" data-template="{{ section.id }}" required {% if enable_options %}class="js-hidden"{% endif %} form="{{ form_id }}">
                                        {%- for variant in product.variants -%}
                                            {% if block.settings.show_unavailable_variants == false and variant.available == false %}{% continue %}{% endif %}
                                            {% capture swatch %}
                                                {% if variant.options.first.swatch.image %}
                                                    data-color="url('{{ variant.options.first.swatch.image | image_url }}')"
                                                {% elsif variant.options.first.swatch.color %}
                                                    data-color="{{ variant.options.first.swatch.color }}"
                                                {% else %}
                                                    data-color="{{ variant.title | split: ' ' | last }}" data-color-class="swatch-custom-color-{{ variant.title | handleize }}"
                                                {% endif %}
                                            {% endcapture %}
                                            <option
                                                    data-l4pr-index="{{ variant.featured_media.position | minus: 1 }}"
                                                    value="{{ variant.id }}"
                                                    data-product-url="{{ variant.product.url }}"
                                                    {% unless variant.available %}data-class="disabled"{% endunless %}
                                                {% if variant == current_variant %}selected{% endif %}
                                                {% if enable_options %}data-options='{"id":{{ variant.id }},"options":{{ variant.options | json | escape }}}'{% endif %}
                                                    {% if variant.image and block.settings.show_variant_images and show_images %}
                                                        data-img="{{ variant.image | image_url: width: image_width_small, height: image_height_small }}"
                                                    {% elsif is_color %}
                                                       {{ swatch }}
                                                    {% endif %}
                                            >{{ variant.title }}</option>
                                        {%- endfor -%}
                                    </select>
                                {%- elsif block.settings.selection_type == 'buttons' -%}
                                    <ul id="f8pr-ul-check-1" class="check {% if show_images == false and is_color %}color{% else %}box{% endif %}{% if enable_options %} js-hidden{% endif %}">
                                        {%- for variant in product.variants -%}
                                            {% if block.settings.show_unavailable_variants == false and variant.available == false %}{% continue %}{% endif %}
                                            {% capture swatch %}
                                                {% if variant.options.first.swatch.image %}
                                                    <i aria-hidden="true" class="icon-circle" style="background-image: url('{{ variant.options.first.swatch.image | image_url }}');"></i>
                                                {% elsif variant.options.first.swatch.color %}
                                                    <i aria-hidden="true" class="icon-circle" style="background-color: {{ variant.options.first.swatch.color }};"></i>
                                                {% else %}
                                                    <i aria-hidden="true" class="icon-circle swatch-custom-color-{{ variant.title | handleize }}" style="background-color: {{ variant.title | split: ' ' | last }};"></i>
                                                {% endif %}
                                            {% endcapture %}
                                            <li>
                                                <input type="radio"
                                                       id="option-{{ variant.id }}-{{ section.id }}"
                                                       value="{{ variant.id }}"
                                                       name="variant-id"
                                                       title="{{ variant.title }}"
                                                       {% if variant == current_variant %}checked{% endif %}
                                                    {% unless variant.available %}class="disabled"{% endunless %}
                                                       data-product-url="{{ variant.product.url }}"
                                                       data-template="{{ section.id }}"
                                                    {% if is_color %}data-change=".data-change-to-{{ section.id }}"{% endif %}
                                                       form="{{ form_id }}"
                                                       data-l4pr-index="{{ variant.featured_media.position | minus: 1 }}"
                                                >
                                                <label for="option-{{ variant.id }}-{{ section.id }}">
                                                    {% if variant.image and block.settings.show_variant_images and show_images %}
                                                        <picture>
                                                            <img src="{{ variant.image | image_url: width: image_width_small, height: image_height_small }}" alt="{{ variant.title }}" width="30" height="23" loading="lazy">
                                                        </picture>
                                                    {% elsif is_color %}
                                                       {{ swatch }}
                                                    {% endif %}
                                                    {% unless is_color %}<span>{{ variant.title }}</span>{% endunless %}
                                                </label>
                                            </li>
                                        {%- endfor -%}
                                    </ul>
                                {%- endif -%}
                            {%- endif -%}
                            <span id="f8pr-pickup-2" class="f8pr-pickup pickup mobile-only size-12{% unless pickup_available %} hidden{% endunless %}"><i aria-hidden="true" class="icon-pin"></i> {{ 'product.pickup_availability.free_pickup' | t }} <a href="#pickup-availability" data-pickup-availability data-id="{{ current_variant.id }}">{{ 'product.pickup_availability.our_stores' | t }} </a></span>
                            {% if enable_options %}
                                <input name="variant-id" id="id-{{ section.id }}" value="{{ current_variant.id }}" type="hidden" form="{{ form_id }}">
                                {%- for option in product.options_with_values -%}
                                    {%- liquid
                                        assign is_color = false
                                        if settings.enable_color_swatches and triggers contains option.name
                                            assign is_color = true
                                        endif

                                        assign options_variants = option.values | map: "variant"
                                        assign options_with_images = options_variants | where: "image"
                                        assign show_images = false
                                        if block.settings.show_variant_images
                                            if option.values.first.product_url
                                                assign show_images = true
                                            elsif is_color
                                                if option.values.size == options_with_images.size
                                                    assign show_images = true
                                                endif
                                            endif
                                        endif
                                    -%}
                                    {% if block.settings.selection_type == 'dropdown' %}
                                        <p class="no-js-hidden">
                                            <label for="option-{{ section.id }}-{{ forloop.index0 }}">
                                                {{ option.name }}
                                                <span id="f8pr-pickup-3" class="f8pr-pickup pickup{% if product.variants.size > 1 %} text-end{% endif %}{% if pickup_available and forloop.first %}{% else %} hidden{% endif %}"><span class="mobile-hide"><i aria-hidden="true" class="icon-pin"></i>{{ 'product.pickup_availability.free_pickup' | t }} <a href="#pickup-availability" data-pickup-availability data-id="{{ current_variant.id }}">{{ 'product.pickup_availability.our_stores' | t }} </a></span></span>
                                            </label>
                                            <select id="option-{{ section.id }}-{{ forloop.index0 }}"
                                                    name="options[{{ section.id }}-{{ option.name | escape }}]"
                                                    data-template="{{ section.id }}"
                                                    form="{{ form_id }}"
                                                    required
                                            >
                                                {%- for value in option.values -%}
                                                    {% capture swatch %}
                                                        {% if value.swatch.image %}
                                                            data-color="url('{{ value.swatch.image | image_url }}')"
                                                        {% elsif value.swatch.color %}
                                                            data-color="{{ value.swatch.color }}"
                                                        {% else %}
                                                            data-color="{{ value | split: ' ' | last }}" data-color-class="swatch-custom-color-{{ value | handleize }}"
                                                        {% endif %}
                                                    {% endcapture %}
                                                    <option value="{{ value | escape }}"
                                                        data-product-url="{{ value.product_url }}"
                                                        data-option-value-id="{{ value.id }}"
                                                        {% if show_images %}
                                                            {%- liquid
                                                                if value.product_url
                                                                    if value.variant
                                                                        assign value_img = value.variant.product.featured_image
                                                                    else
                                                                        assign value_product_handle = value.product_url | split: '/' | last
                                                                        assign value_product = all_products[value_product_handle]
                                                                        assign value_img = value_product.featured_image
                                                                    endif
                                                                else
                                                                    assign value_img = value.variant.image
                                                                endif
                                                            -%}
                                                            {% if value_img %}
                                                              data-img="{{ value_img | image_url: width: image_width_small, height: image_height_small }}"
                                                            {% endif %}
                                                        {% elsif is_color %}
                                                           {{ swatch }}
                                                        {% endif %}
                                                            {% if value.selected %}selected{% endif %}
                                                            {% unless value.variant.available %}
                                                                data-class="disabled"
                                                            {% endunless %}
                                                            data-l4pr-index="{{ value.variant.featured_media.position | minus: 1 }}"
                                                    >
                                                        {{ value }}
                                                    </option>
                                                {%- endfor -%}
                                            </select>
                                        </p>
                                    {% else %}
                                        <label for="option-{{ section.id }}-{{ forloop.index0 }}" class="no-js-hidden">
                                            {{ option.name }}{% if is_color %}<span class="data-change-to-option-{{ section.id }}-{{ forloop.index0 }}">{{ option.selected_value }}</span>{% endif %}
                                            <span id="f8pr-pickup-4" class="f8pr-pickup pickup{% if product.variants.size > 1 %} text-end{% endif %}{% if pickup_available and forloop.first %}{% else %} hidden{% endif %}"><span class="mobile-hide"><i aria-hidden="true" class="icon-pin"></i>{{ 'product.pickup_availability.free_pickup' | t }} <a href="#pickup-availability" data-pickup-availability data-id="{{ current_variant.id }}">{{ 'product.pickup_availability.our_stores' | t }} </a></span></span>
                                        </label>
                                        <ul id="f8pr-ul-check-{{ forloop.index | plus: 1 }}" class="check {% if show_images == false and is_color %}color{% else %}box{% endif %}" class="no-js-hidden">
                                            {%- for value in option.values -%}
                                                {% capture swatch %}
                                                    {% if value.swatch.image %}
                                                        <i aria-hidden="true" class="icon-circle" style="background-image: url('{{ value.swatch.image | image_url }}');"></i>
                                                    {% elsif value.swatch.color %}
                                                        <i aria-hidden="true" class="icon-circle" style="background-color: {{ value.swatch.color }};"></i>
                                                    {% else %}
                                                        <i aria-hidden="true" class="icon-circle swatch-custom-color-{{ value | handleize }}" style="background-color: {{ value | split: ' ' | last }};"></i>
                                                    {% endif %}
                                                {% endcapture %}
                                                <li>
                                                    <input type="radio"
                                                           data-product-url="{{ value.product_url }}"
                                                           data-option-value-id="{{ value.id }}"
                                                           id="option-{{ section.id }}-{{ forloop.parentloop.index0 }}-{{ forloop.index0 }}"
                                                           value="{{ value | escape }}"
                                                           name="options[{{ section.id }}-{{ option.name | escape }}]"
                                                           title="{{ value }}"
                                                           {% if value.selected %}checked{% endif %}
                                                           data-template="{{ section.id }}"
                                                        {% if is_color %}data-change=".data-change-to-option-{{ section.id }}-{{ forloop.parentloop.index0 }}"{% endif %}
                                                           form="{{ form_id }}"
                                                            {% unless value.variant.available %}
                                                                class="disabled"
                                                            {% endunless %}
                                                           data-l4pr-index="{{ value.variant.featured_media.position | minus: 1 }}"
                                                    >
                                                    <label for="option-{{ section.id }}-{{ forloop.parentloop.index0 }}-{{ forloop.index0 }}">
                                                        {% if show_images -%}
                                                            {%- liquid
                                                                if value.product_url
                                                                    if value.variant
                                                                        assign value_img = value.variant.product.featured_image
                                                                    else
                                                                        assign value_product_handle = value.product_url | split: '/' | last
                                                                        assign value_product = all_products[value_product_handle]
                                                                        assign value_img = value_product.featured_image
                                                                    endif
                                                                else
                                                                    assign value_img = value.variant.image
                                                                endif
                                                            -%}
                                                            {% if value_img %}
                                                              <picture>
                                                                  <img src="{{ value_img | image_url: width: image_width_small, height: image_height_small }}" alt="{{ value }}" width="30" height="23" loading="lazy">
                                                              </picture>
                                                            {% endif %}
                                                            {% unless is_color %}
                                                                <span>{{ value }}</span>
                                                            {% endunless %}
                                                        {% elsif is_color %}
                                                            {{ swatch }}
                                                        {% else %}
                                                            <span>{{ value }}</span>
                                                        {% endif %}
                                                    </label>
                                                </li>
                                            {%- endfor -%}
                                        </ul>
                                    {% endif %}
                                {%- endfor -%}
                            {% endif %}

                        {%- endif -%}
                        {% if product.selling_plan_groups.size > 0 and block.settings.enable_selling_plans %}
                            {%- if product.selling_plan_groups.size > 1 or product.requires_selling_plan == false -%}
                                <h2 class="label">{{ 'product.form.choose_option' | t }}</h2>
                                <ul class="check inline">
                                    {%- unless product.requires_selling_plan -%}
                                        <li>
                                            <input type="radio" id="purchase_option_single-{{ section.id }}" name="selling_plan_group" value="" data-enable data-template="{{ section.id }}" form="{{ form_id }}" required>
                                            <label for="purchase_option_single-{{ section.id }}">{{ 'product.one_time_purchase' | t }}</label>
                                        </li>
                                    {%- endunless -%}
                                    {%- for group in product.selling_plan_groups -%}
                                        <li>
                                            <input type="radio" id="{{ group.id }}-{{ section.id }}" name="selling_plan_group" value="" data-enable="checkout-type-{{ section.id }}-{{ group.id }}" data-template="{{ section.id }}" form="{{ form_id }}" required>
                                            <label for="{{ group.id }}-{{ section.id }}">{{ group.name }}</label>
                                        </li>
                                    {%- endfor -%}
                                </ul>
                            {%- else -%}
                                {%- assign default_plan = product.selling_plan_groups | first -%}
                                <input type="hidden" id="{{ default_plan.id }}-{{ section.id }}" name="selling_plan_group" value="" data-enable="checkout-type-{{ section.id }}-{{ default_plan.id }}" form="{{ form_id }}" required checked>
                            {%- endif -%}
                            {%- for group in product.selling_plan_groups -%}
                                <p class="m15 f8pr-selling-plan {% if product.requires_selling_plan == false or product.selling_plan_groups.size > 1 %}hidden{% endif %}" data-element="checkout-type-{{ section.id }}-{{ group.id }}">
                                    <span class="label">{{ 'product.form.purchase_options_title' | t }}</span>
                                    <span class="check wide">
                            {%- for plan in group.selling_plans -%}
                                {%- liquid
                                    if plan.price_adjustments
                                        case plan.price_adjustments.first.value_type
                                            when 'percentage'
                                                assign new_price_3 = current_variant.price | times: 1.0 | divided_by: 100
                                                assign new_price_2 = new_price_3 | times: plan.price_adjustments.first.value
                                                assign new_price = current_variant.price | minus: new_price_2
                                            when 'fixed_amount'
                                                assign new_price = current_variant.price | minus: plan.price_adjustments.first.value
                                            when 'price'
                                                assign new_price = plan.price_adjustments.first.value
                                        endcase
                                        assign saved_amount = current_variant.price | minus: new_price
                                    endif
                                -%}
                                <span>
                                <input type="radio" id="{{ plan.id }}-{{ section.id }}" name="selling_plan" value="{{ plan.id }}" form="{{ form_id }}" {% if product.selling_plan_groups.size == 1 and product.requires_selling_plan == true %}required{% endif %}>
                                <label for="{{ plan.id }}-{{ section.id }}">{{ plan.name }}</label>
                                <span class="s1pr">
                                  {% if new_price > 0 %}
                                      {% if saved_amount > 0 %}<span class="old-price">{{ current_variant.price | money }}</span>{% endif %}
                                    {{ new_price | money }}
                                    {% if saved_amount > 0 %}<span class="small overlay-valid">{{ 'product.save' | t }}&nbsp;{{ saved_amount | money }}</span>{% endif %}
                                  {% else %}
                                      {{ current_variant.price | money }}
                                  {% endif %}
                                </span>
                              </span>
                            {%- endfor -%}
                          </span>
                                </p>
                            {%- endfor -%}
                        {% endif %}
                    </div>
                {%- when 'inventory' -%}
                    <div class="f8pr-stock m0" {% if preorder and settings.show_preorder_inventory == false %}style="display:none"{% endif %}>
                        {%- liquid
                            assign next_block = forloop.index
                            if section.blocks[next_block].type == 'price'
                                assign no_margin = true
                            endif
                        -%}
                        {%- render 'product-deliverytime',
                                product: product,
                                current_variant: current_variant,
                        container: "p",
                                origin: 'productpage',
                                no_margin: no_margin,
                                shopify_attributes: block.shopify_attributes
                        -%}
                    </div>
                {%- when 'codes' -%}
                    <div class="f8pr-codes">
                            {%- liquid
                                if block.settings.show_sku and current_variant.sku != empty and current_variant.sku != nil
                                    assign show_sku = true
                                endif
                                if block.settings.show_barcode and current_variant.barcode != empty and current_variant.barcode != nil
                                    assign show_barcode = true
                                endif
                            -%}
                            {%- if show_sku or show_barcode -%}
                                <p {{ block.shopify_attributes }}>
                                    {%- if show_sku -%}
                                        <span class="strong">{{ 'product.sku' | t }}:</span> {{ current_variant.sku }}
                                    {%- endif -%}
                                    {%- if show_sku and show_barcode -%}<br>{%- endif -%}
                                    {%- if show_barcode -%}
                                        <span class="strong">{{ 'product.barcode' | t }}:</span> {{ current_variant.barcode }}
                                    {%- endif -%}
                                </p>
                            {%- endif -%}
                    </div>
                {%- when 'price' -%}
                    <p class="f8pr-price s1pr price" {{ block.shopify_attributes }}>
                        {%- if current_variant.compare_at_price > current_variant.price -%}
                            <span class="old-price">{{ current_variant.compare_at_price | money }}</span>&nbsp;
                        {%- endif -%}
                        {{ current_variant.price | money }}{% if block.settings.show_tax %} {% if cart.taxes_included %}{{ 'product.including_tax' | t }}{% else %}{{ 'product.excluding_tax' | t }}{% endif %}{% endif %}
                        {%- if current_variant.unit_price_measurement -%}
                            <span class="small">{{ 'product.unit_price_label' | t }}{{ current_variant.unit_price | unit_price_with_measurement: current_variant.unit_price_measurement }}</span>
                        {%- endif -%}
                    </p>
                    {%- assign product_form_installment_id = 'product-form-installment-' | append: section.id -%}
                    {%- form 'product', product, id: product_form_installment_id, class: 'f8pr-product-form-installment' -%}
                        <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                        <div class="shopify-installments-container">{{ form | payment_terms }}</div>
                    {%- endform -%}
                {%- when 'features_cards' -%}
                    <div class="features-cards-block" {{ block.shopify_attributes }}>
                        <div class="features-cards-container">
                            {%- if block.settings.card1_text != blank or block.settings.card1_svg != blank -%}
                                <div class="feature-card">
                                    {%- if block.settings.card1_svg != blank -%}
                                        <div class="feature-card-icon">
                                            {{ block.settings.card1_svg }}
                                        </div>
                                    {%- endif -%}
                                    {%- if block.settings.card1_text != blank -%}
                                        <div class="feature-card-text">{{ block.settings.card1_text }}</div>
                                    {%- endif -%}
                                </div>
                            {%- endif -%}

                            {%- if block.settings.card2_text != blank or block.settings.card2_svg != blank -%}
                                <div class="feature-card">
                                    {%- if block.settings.card2_svg != blank -%}
                                        <div class="feature-card-icon">
                                            {{ block.settings.card2_svg }}
                                        </div>
                                    {%- endif -%}
                                    {%- if block.settings.card2_text != blank -%}
                                        <div class="feature-card-text">{{ block.settings.card2_text }}</div>
                                    {%- endif -%}
                                </div>
                            {%- endif -%}

                            {%- if block.settings.card3_text != blank or block.settings.card3_svg != blank -%}
                                <div class="feature-card">
                                    {%- if block.settings.card3_svg != blank -%}
                                        <div class="feature-card-icon">
                                            {{ block.settings.card3_svg }}
                                        </div>
                                    {%- endif -%}
                                    {%- if block.settings.card3_text != blank -%}
                                        <div class="feature-card-text">{{ block.settings.card3_text }}</div>
                                    {%- endif -%}
                                </div>
                            {%- endif -%}
                        </div>
                    </div>

                {%- when 'guarantee_banner' -%}
                    <div class="guarantee-banner-block" {{ block.shopify_attributes }}>
                        {%- if block.settings.guarantee_text != blank -%}
                            <div class="guarantee-text">{{ block.settings.guarantee_text }}</div>
                        {%- endif -%}

                        <div class="guarantee-card"
                             style="background: linear-gradient(180deg, {{ block.settings.card_bg_color | default: '#1E306E' }} 0%, #0575E6 100%); color: {{ block.settings.card_text_color | default: '#FFFFFF' }};">

                            <div class="guarantee-card-content">
                                {%- if block.settings.card_title != blank -%}
                                    <h3 class="guarantee-card-title">{{ block.settings.card_title }}</h3>
                                {%- endif -%}

                                {%- if block.settings.show_learn_more -%}
                                    <button class="guarantee-learn-more" onclick="openGuaranteePopup('{{ section.id }}{{ block.id }}')">
                                        <svg width="15" height="15" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="8" cy="8" r="8" fill="currentColor" opacity="0.2"/>
                                            <path d="M8 12V8M8 4H8.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        {{ block.settings.learn_more_text | default: 'Learn more' }}
                                    </button>
                                {%- endif -%}
                            </div>
                        </div>

                        <!-- Guarantee Popup -->
                        <div id="guarantee-popup-overlay-{{ section.id }}{{ block.id }}" class="guarantee-popup-overlay" onclick="closeGuaranteePopup('{{ section.id }}{{ block.id }}')">
                            <div class="guarantee-popup-content" onclick="event.stopPropagation()">
                                <button class="guarantee-popup-close" onclick="closeGuaranteePopup('{{ section.id }}{{ block.id }}')">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M15 5L5 15M5 5L15 15" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>

                                {%- if block.settings.popup_title != blank -%}
                                    <h3 class="guarantee-popup-title">{{ block.settings.popup_title }}</h3>
                                {%- endif -%}

                                {%- if block.settings.popup_description != blank -%}
                                    <p class="guarantee-popup-description">{{ block.settings.popup_description }}</p>
                                {%- endif -%}

                                {%- if block.settings.popup_subtitle != blank -%}
                                    <h4 class="guarantee-popup-subtitle">{{ block.settings.popup_subtitle }}</h4>
                                {%- endif -%}

                                {%- if block.settings.popup_content != blank -%}
                                    <div class="guarantee-popup-text">{{ block.settings.popup_content }}</div>
                                {%- endif -%}
                            </div>
                        </div>
                    </div>
                {%- when 'buy_button' -%}
                    {%- liquid
                        assign gift_card_recipient_feature_active = false
                        if block.settings.show_gift_card_recipient and product.gift_card?
                            assign gift_card_recipient_feature_active = true
                        endif
                        assign show_dynamic_buybutton = false
                        if block.settings.show_dynamic_buybutton and gift_card_recipient_feature_active == false
                            assign show_dynamic_buybutton = true
                        endif
                        if preorder
                            if block.settings.preorder_show_dynamic_buybutton == false
                                assign show_dynamic_buybutton = false
                            endif
                            if block.settings.preorder_label != empty
                                assign preorder_label = block.settings.preorder_label | remove: '</p>' | remove: '<p>'
                                assign input_label = 'product.form.pre_order_info' | t
                                if input_label == empty
                                    assign input_label = 'Delivery'
                                endif
                            endif
                        elsif current_variant.available == false
                          assign show_dynamic_buybutton = false
                        endif
                    -%}
                    {%- form 'product', product, id: form_id, class: form_class, autocomplete: "off" -%}
                        <input type="hidden" name="id" value="{{ current_variant.id }}" class="product-variant-id">
                        {%- if gift_card_recipient_feature_active -%}
                            {%- render 'gift-card-recipient-form', product: product, form: form, section: section -%}
                        {%- endif -%}
                        <div class="submit m10 {% unless current_variant.available %}unavailable{% endunless %}" {{ block.shopify_attributes }}>
                          <span class="input-amount f8pr-amount" {% unless block.settings.show_amount_selection -%}style="display:none;"{% endunless %}>
                              <label for="quantity" class="hidden">{{ 'product.form.quantity' | t }}</label>
                              <input type="number" id="quantity" name="quantity" data-link="#product_qty_sticky" value="{{ current_variant.quantity_rule.min | default: 1 }}"
                                min="{{ current_variant.quantity_rule.min | default: 1 }}"
                                {% if current_variant.inventory_management == 'shopify' and current_variant.inventory_policy == 'deny' -%}
                                    max="{{ current_variant.inventory_quantity }}"
                                {% elsif current_variant.quantity_rule.max %}
                                    max="{{ current_variant.quantity_rule.max }}"
                                {% endif %}
                                {% if current_variant.quantity_rule.increment %}step="{{ current_variant.quantity_rule.increment }}"{% endif %}
                                required>
                          </span>
                          <div class="f8pr-button wide">
                          {%- if current_variant == null -%}
                              <button type="submit" disabled class="disabled visible overlay-unavailable_buy_button{% if block.settings.button_style == 'inv' %} inv{% endif %}">{{ 'product.form.unavailable' | t }}</button>
                          {%- elsif preorder -%}
                              <button type="submit" class="overlay-preorder{% if block.settings.button_style == 'inv' %} inv{% endif %}">{{ block.settings.preorder_button_text }}</button>
                          {%- elsif current_variant.available -%}
                              <button type="submit" class="overlay-buy_button{% if block.settings.button_style == 'inv' %} inv{% endif %}">{{ 'product.form.add_to_cart' | t }}</button>
                          {%- else -%}
                              <button type="submit" class="disabled visible overlay-unavailable_buy_button{% if block.settings.button_style == 'inv' %} inv{% endif %}">{{ 'product.form.not_in_stock' | t }}</button>
                          {%- endif -%}
                          </div>
                          <span class="wishlist-productpage hidden"><a href="#wishlist" aria-label="Wishlist" data-product-id="{{ product.id }}" data-variant-id="{{ current_variant.id }}"><i class="icon-heart-outline"></i></a></span>
                        </div>
                        {%- if show_dynamic_buybutton -%}
                            <div class="overlay-dynamic_buy_button {{ block.settings.button_style_dynamic_buybutton }}-btn">
                                {{ form | payment_button }}
                            </div>
                        {%- endif -%}
                        <ul class="f8pr-preorder l4al inline{% unless preorder and preorder_label %} m0{% endunless %}">
                            {%- if preorder and preorder_label -%}
                                <li class="overlay-{{ block.settings.preorder_label_color_palette }} text-center">
                                    <span><i aria-hidden="true" class="icon-check"></i>&nbsp;{{ preorder_label }}</span>
                                </li>
                                <input class="hidden" id="{{ input_label | handleize }}" type="text" name="properties[{{ input_label }}]" value="{{ preorder_label | strip_html }}">
                            {%- endif -%}
                        </ul>
                {%- endform -%}
                {%- when 'usp' -%}
                    <ul class="l4us" {{ block.shopify_attributes }}>
                        {%- if block.settings.usp != empty -%}<li>{{ block.settings.usp | remove: '<p>' | remove: '</p>' }}</li>{%- endif -%}
                    </ul>
                {%- when 'trustbadge' -%}
                    {%- liquid
                        assign block_attributes = block.shopify_attributes
                        render 'trustbadge', shopify_attributes: block_attributes
                    -%}
                    {%- when 'upsell' -%}
                      <link href="{{ 'page-product.css' | asset_url }}" rel="preload" as="style" onload="this.rel='stylesheet'">
                      <noscript><link rel="stylesheet" href="{{ 'page-product.css' | asset_url }}"></noscript>
                    {%- liquid
                      assign product_list = block.settings.product_list
                      assign products_count = product_list.count
                      unless block.settings.show_out_of_stock_products
                          assign product_list = block.settings.product_list | where: 'available'
                          assign products_count = product_list.size
                      endunless  
                      assign visible_products = block.settings.visible_products
                    -%}
                    {%- if products_count != 0 or request.design_mode -%}
                      <div class="f8pr-bulk{% if current_variant.available == false %} hidden{% endif %}" {{ block.shopify_attributes }}>
                        <{{ block.settings.heading_size }} class="strong {% if block.settings.heading_size contains "p" %}m10{% else %}m20{% endif %} {% if block.settings.heading_position == 'center' %}text-center{% endif %}">{{ block.settings.heading }}</{{ block.settings.heading_size }}>
                        <ul class="palette-{{ block.settings.color_scheme }} module-color-palette upsell-items l4cl box">
                          {%- if products_count > 0 -%}
                            {%- for product in product_list -%}
                                {% if product.requires_selling_plan == true %}{% continue %}{% endif %}
                                {%- liquid
                                    capture placeholder_int
                                        cycle 1, 2, 3, 4, 5, 6
                                    endcapture
                                -%}
                                {%- render 'product-item',
                                  product: product,
                                  bulk: true,
                                  variant_in_popup: block.settings.variant_in_popup,
                                  index: forloop.index,
                                  block_id: block.id,
                                  visible_products: visible_products,
                                  variant_selector_color: block.settings.variant_selector_color_scheme,
                                  placeholder_int: placeholder_int,
                                  layout: 'list',
                                  show_image: true,
                                  show_title: true,
                                  show_price: true
                                -%}
                            {%- endfor -%}
                          {%- elsif request.design_mode -%}
                            {%- for product in (1..visible_products) -%}
                                {%- liquid
                                    capture placeholder_int
                                        cycle 1, 2, 3, 4, 5
                                    endcapture
                                -%}
                                {%- render 'product-item',
                                  product: blank,
                                  bulk: true,
                                  variant_in_popup: block.settings.variant_in_popup,
                                  placeholder_int: placeholder_int,
                                  layout: 'list',
                                  show_image: true,
                                  show_title: true,
                                  show_price: true
                                -%}
                            {%- endfor -%}
                          {%- endif -%}
                        </ul>
                        {% if products_count > visible_products %}
                          <p class="link-btn {% if block.settings.heading_position == 'center' %}text-center{% endif %}">
                            <a href="./" class="inline overlay-gray font-regular" data-toggle="unique-more-products-class">{{ 'product.form.show_more' | t }} <i aria-hidden="true" class="icon-chevron-down"></i></a>
                          </p>
                        {% endif %}
                      </div>
                    {%- endif -%}
                {%- when 'urgency' -%}
                    {%- liquid
                      assign min_stock = block.settings.min_stock | at_least: 0
                      if block.settings.min_stock_meta != blank
                        assign min_stock = block.settings.min_stock_meta | times: 1
                      endif
                      assign color_progressbar = block.settings.gradient_progressbar
                      if color_progressbar == blank
                        assign color_progressbar = block.settings.color_progressbar
                      endif
                      assign show_bar = true
                      if current_variant.available == false and block.settings.show_bar_unavailable == false and current_variant.inventory_quantity <= 0
                        assign show_bar = false
                      elsif current_variant.available and current_variant.inventory_quantity <= 0 and block.settings.show_bar_available_nostock == false
                        assign show_bar = false
                      endif
                      assign overlay = false
                      if block.settings.color_palette.id != settings.default_color_scheme.id
                        assign overlay = true
                      endif
                    -%}
                    {%- capture message -%}
                      {% if current_variant.inventory_quantity <= 0 %}
                        {{ block.settings.outofstock_message }}
                      {% else %}
                        {{ block.settings.message | replace: '*inventory*', current_variant.inventory_quantity }}
                      {% endif %}
                    {%- endcapture -%}
                    <div class="f8pr-urgency" {{ block.shopify_attributes }}>
                      {% if show_bar and min_stock >= current_variant.inventory_quantity %}
                        {% if overlay %}<div class="palette-{{ block.settings.color_palette }} module-color-palette m6bx" style="--m6bx_bw: 0px; --dist_a: 20px; --dist_b: var(--dist_a);">{% endif %}
                          <p class="m20">
                            <label for="fpt" class="hidden">Quantity left</label>
                            {%- if block.settings.position_stock_message == 'above' -%}
                              <span class="m5">{{ message }}</span>
                            {%- endif -%}
                            <span class="input-range single tip{% if block.settings.show_level == false %} tip-hidden{% endif %}{% if block.settings.position_stock_message == 'above' %} inv{% endif %}{% if block.settings.layout == 'medium' %} solid{% endif %}" style="--range_bg: {{ color_progressbar }}; --range_bg_blank: {{ block.settings.background_progress_bar }}; --range_tip_bg: {{ block.settings.tooltip_background }}; --range_tip_fg: {{ block.settings.tooltip_text_color }}; --range_fg: {{ block.settings.tooltip_text_color }};">
                              <input type="number" id="{{ forloop.index }}-fpt" name="{{ forloop.index }}-fpt" value="{{ current_variant.inventory_quantity }}" disabled min="0" max="{{ min_stock }}">
                            </span>
                            {%- if block.settings.position_stock_message == 'below' -%}
                              <span>{{ message }}</span>
                            {%- endif -%}
                          </p>
                        {% if overlay %}</div>{% endif %}
                      {% endif %}
                    </div>
                {%- when 'quantity_rules' -%}
                    {%- liquid
                        assign background_color = true
                        if block.settings.color_palette.id == settings.default_color_scheme.id
                            assign background_color = false
                        endif
                    -%}
                    <div class="f8pr-quantity-rules" {{ block.shopify_attributes }}>
                        {% assign show_quantity_rules = false %}
                        {% assign minimum = false %}
                        {% assign maximum = false %}
                        {% assign increment = false %}
                        {%- if product.selected_or_first_available_variant.quantity_rule.min > 1 -%}
                            {% assign minimum = true %}
                            {% assign show_quantity_rules = true %}
                        {%- endif -%}
                        {%- if product.selected_or_first_available_variant.quantity_rule.max != null -%}
                            {% assign maximum = true %}
                            {% assign show_quantity_rules = true %}
                        {%- endif -%}
                        {%- if product.selected_or_first_available_variant.quantity_rule.increment > 1 -%}
                            {% assign increment = true %}
                            {% assign show_quantity_rules = true %}
                        {%- endif -%}
                        {% if request.design_mode or show_quantity_rules %}
                            <div class="{% if background_color %}palette-{{ block.settings.color_palette }} module-color-palette m6bx compact{% endif %}">
                                {% if block.settings.text %}{{ block.settings.text }}{% endif %}
                                <p>
                                    {% if request.design_mode %}
                                        {{- 'product.volume_pricing.minimum_of' | t: quantity: '2' | capitalize }} / {{ 'product.volume_pricing.maximum_of' | t: quantity: '20' }} / {{ 'product.volume_pricing.multiples_of' | t: quantity: '2' -}}
                                    {% else %}
                                        {%- if minimum -%}
                                            {{- 'product.volume_pricing.minimum_of' | t: quantity: product.selected_or_first_available_variant.quantity_rule.min | capitalize -}}
                                        {%- endif -%}
                                        {%- if maximum -%}
                                            {% if minimum %}
                                                / {{ 'product.volume_pricing.maximum_of' | t: quantity: product.selected_or_first_available_variant.quantity_rule.max -}}
                                            {% else %}
                                                {{- 'product.volume_pricing.maximum_of' | t: quantity: product.selected_or_first_available_variant.quantity_rule.max | capitalize -}}
                                            {% endif %}
                                        {%- endif -%}
                                        {%- if increment -%}
                                            {% if minimum or maximum %} / {% endif %}
                                            {{ 'product.volume_pricing.multiples_of' | t: quantity: product.selected_or_first_available_variant.quantity_rule.increment -}}
                                        {%- endif -%}
                                    {% endif %}
                                </p>
                            </div>
                        {% endif %}
                    </div>
                {%- when 'volume_pricing' -%}
                    {%- liquid
                        assign background_color = true
                        if block.settings.color_palette.id == settings.default_color_scheme.id
                            assign background_color = false
                        endif

                        assign show_volume_pricing = false
                        if product.quantity_price_breaks_configured? and product.selected_or_first_available_variant.quantity_price_breaks.size > 0
                            assign show_volume_pricing = true
                        endif
                    -%}
                    <div class="f8pr-volume-pricing" {{ block.shopify_attributes }}>
                        {% if request.design_mode or show_volume_pricing %}
                            <div class="{% if background_color %}palette-{{ block.settings.color_palette }} module-color-palette m6bx compact{% endif %}">
                                {% if block.settings.text %}{{ block.settings.text }}{% endif %}
                                <p>
                                    {% if request.design_mode %}
                                        {{ 'product.volume_pricing.price_at_each_html' | t: quantity: '2', price: '€95,00' }}<br/>
                                        {{ 'product.volume_pricing.price_at_each_html' | t: quantity: '4', price: '€85,00' }}
                                    {% else %}
                                        {%- for price_break in product.selected_or_first_available_variant.quantity_price_breaks -%}
                                            {%- assign price = price_break.price | money -%}
                                            {{ 'product.volume_pricing.price_at_each_html' | t: quantity: price_break.minimum_quantity, price: price }}
                                            {%- unless forloop.last -%}<br />{%- endunless -%}
                                        {%- endfor -%}
                                    {% endif %}
                                </p>
                            </div>
                        {% endif %}
                    </div>
                {%- when 'shipping_timer' -%}
                    {%- if current_variant.requires_shipping -%}
                        {% assign set_hidden = false %}
                        {% if current_variant.available == false and settings.shipping_timer_show_unavailable == false %}{% assign set_hidden = true %}{% endif %}
                        <ul class="l4us f8pr-shipping-timer"{% if preorder or set_hidden %} style="height: 0; opacity: 0; visibility: hidden; "{% endif %} {{ block.shopify_attributes }}>
                            {%- render 'shipping-timer' -%}
                        </ul>
                    {%- endif -%}
                {%- else -%}
                    {%- if block.type == 'content' -%}
                        {%- liquid
                            assign show = false
                            if block.settings.text != empty or block.settings.page != blank or block.settings.image != blank or block.settings.liquid != blank or block.settings.html != blank or block.settings.show_contact_form
                                assign show = true
                            elsif block.settings.layout == 'none'
                                assign show = true
                            endif
                        -%}
                    {%- endif -%}
                    {%- if show or block.type == 'complementary_products' -%}
                        {% capture block_attributes %}
                            {% if block.type == 'complementary_products' %}
                                data-template="{{ section.id }}" data-product-id="{{ product.id }}" data-intent="complementary"
                                {% if recommendations.products_count == 0 and request.design_mode == false %}data-hide{% endif %}
                            {%- endif -%}
                        {%- endcapture -%}
                        {%- capture header_img -%}
                            {%- if block.settings.header_image or block.settings.header_image_svg -%}
                                {%- assign image_width = block.settings.header_image_width -%}
                                {%- assign image_width_2 = image_width | times: 2 -%}
                                <img
                                        {% if block.settings.header_image_svg %}
                                            src="{{ block.settings.header_image_svg }}"
                                        {% elsif block.settings.header_image	%}
                                            src="{{ block.settings.header_image | image_url: width: image_width }}"
                                            srcset="{{ block.settings.header_image | image_url: width: image_width }} 1x,{{ block.settings.header_image | image_url: width: image_width_2 }} 2x"
                                        {% endif %}
                                        height="20"
                                        width="{{ image_width }}"
                                        style="width:{{ image_width }}px!important"
                                        alt="{{ block.settings.header_image.alt | default: block.settings.title | escape }}"
                                        loading="lazy"
                                >
                            {%- elsif block.settings.icon != 'none' -%}
                                {%- render 'icons', icon: block.settings.icon -%}&nbsp;
                                {% style %}
                                    #shopify-section-{{ section.id }} .block-{{ block.id }} svg, #quickshop .block-{{ block.id }} svg { height: {% if block.settings.enable_tab or block.settings.layout != 'none' %}20{% else %}25{% endif %}px!important; width: {% if block.settings.enable_tab or block.settings.layout != 'none' %}20{% else %}25{% endif %}px!important; }
                                {% endstyle %}
                            {%- endif -%}
                        {%- endcapture -%}
                        {%- capture content -%}
                            {%- if block.type == 'content' -%}
                                {%- if block.settings.text != empty -%}{{ block.settings.text }}{%- endif -%}
                                {%- if block.settings.page != blank -%}{{ block.settings.page.content }}{%- endif -%}
                                {%- if block.settings.image != blank -%}
                                    <div class="m20">
                                        <img
                                                src="{{ block.settings.image | image_url: width: block.settings.image_width }}"
                                                srcset="{% render 'image-srcset', image: block.settings.image %}"
                                                sizes="
                        (min-width: 760px) 500px
                        100vw
                        "
                                                alt="{{ block.settings.image.alt | default: block.settings.title | escape }}"
                                                width="{{ block.settings.image_width }}"
                                                height="640"
                                                loading="lazy"
                                        >
                                    </div>
                                {%- endif -%}
                                {%- if block.settings.liquid != empty -%}{{ block.settings.liquid }}{%- endif -%}
                                {%- if block.settings.html != empty -%}{{ block.settings.html }}{%- endif -%}
                                {%- if block.settings.show_contact_form -%}
                                    {%- assign contactform_classes = 'f8cm f8vl w940 m30 base-font' -%}
                                    {%- assign contactform_id = section.id | append: '-' | append: block.id -%}
                                    {%- form 'contact', id: contactform_id, class: contactform_classes -%}
                                        {%- if form.errors -%}
                                            <script>
                                                document.addEventListener('DOMContentLoaded', function () {
                                                    var alertAttributes = { message: "{{ 'service.contact_form.email' | t }} {{ form.errors.messages['email'] }}", type: "error", origin: "{{ contactform_id }}" },
                                                        showAlertEvent = new CustomEvent("showAlert", {detail: alertAttributes});
                                                    window.dispatchEvent(showAlertEvent);
                                                });
                                            </script>
                                        {%- elsif form.posted_successfully? -%}
                                            <script>
                                                document.addEventListener('DOMContentLoaded', function () {
                                                    var alertAttributes = { message: "{{ 'service.contact_form.posted_successfully' | t }}", type: "success", origin: "{{ contactform_id }}" },
                                                        showAlertEvent = new CustomEvent("showAlert", {detail: alertAttributes});
                                                    window.dispatchEvent(showAlertEvent);
                                                });
                                            </script>
                                        {%- endif -%}
                                        <fieldset>
                                            <legend>{{ 'service.contact_form.title' | t }}</legend>
                                            <p>
                                                <label for="subject">{{ 'service.contact_form.subject' | t }}</label>
                                                <input type="text" id="subject" name="contact[subject]" placeholder="{{ 'service.contact_form.subject' | t }}">
                                            </p>
                                            <div class="cols">
                                                <p class="w50">
                                                    <label for="name">{{ 'service.contact_form.name' | t }}</label>
                                                    <input type="text" id="name" name="contact[name]" placeholder="{{ 'service.contact_form.name' | t }}">
                                                </p>
                                                <p class="w50">
                                                    <label for="email">{{ 'service.contact_form.email' | t }}<span class="overlay-theme">*</span></label>
                                                    <input type="email" id="email" name="contact[email]" placeholder="{{ 'service.contact_form.email' | t }}" required>
                                                </p>
                                            </div>
                                            <p>
                                                <label for="body">{{ 'service.contact_form.message' | t }}<span class="overlay-theme">*</span></label>
                                                <textarea id="message" name="contact[body]" placeholder="{{ 'service.contact_form.message' | t }}" required></textarea>
                                            </p>
                                            <p class="submit m0"><button type="submit">{{ 'service.contact_form.send' | t }}</button></p>
                                        </fieldset>
                                    {% endform %}
                                {%- endif -%}
                            {%- elsif block.type == 'complementary_products' -%}
                                {%- liquid
                                    if block.settings.layout == 'grid'
                                    assign limit = block.settings.number_of_items | at_most: recommendations.products_count
                                    if limit == 0
                                        assign limit = block.settings.number_of_items
                                    endif
                                    case limit
                                        when 1
                                            assign width_class = 'w100'
                                        when 2
                                            assign width_class = 'w50'
                                        when 3
                                            assign width_class = 'w33'
                                        when 4
                                            assign width_class = 'w25'
                                        when 5
                                            assign width_class = 'w20'
                                    endcase
                                    endif
                                -%}
                                <ul class="l4cl {% if block.settings.layout == 'list' %}hr inv{% unless block.settings.products_show_image %} no-img{% endunless %}{% else %}compact {{ width_class }}{% endif %}">
                                    {%- if recommendations.performed and recommendations.products_count > 0 -%}
                                        {%- for product in recommendations.products -%}
                                            {%- liquid
                                                capture placeholder_int
                                                    cycle 1, 2, 3, 4, 5, 6
                                                endcapture
                                            -%}
                                            {%- render 'product-item',
                                                    product: product,
                                                    placeholder_int: placeholder_int,
                                                    enable_quick_buy_desktop: block.settings.enable_quick_buy_desktop,
                                                    enable_quick_buy_mobile: block.settings.enable_quick_buy_mobile,
                                                    enable_quick_buy_qty_selector: block.settings.enable_quick_buy_qty_selector,
                                                    quick_buy_compact: block.settings.enable_quick_buy_compact,
                                                    enable_quick_buy_drawer: block.settings.enable_quick_buy_drawer,
                                                    layout: block.settings.layout,
                                                    show_image: block.settings.products_show_image,
                                                    show_vendor: block.settings.products_show_vendor,
                                                    show_title: block.settings.products_show_title,
                                                    show_rating: block.settings.products_show_rating,
                                                    show_price: block.settings.products_show_price,
                                                    show_stock: block.settings.products_show_stock,
                                                    enable_color_picker: block.settings.enable_color_picker
                                            -%}
                                        {%- endfor -%}
                                    {%- elsif request.design_mode -%}
                                        {%- for product in (1..5) -%}
                                            {%- liquid
                                                capture placeholder_int
                                                    cycle 1, 2, 3, 4, 5
                                                endcapture
                                            -%}
                                            {%- render 'product-item',
                                                    product: blank,
                                                    placeholder_int: placeholder_int,
                                                    enable_quick_buy_desktop: block.settings.enable_quick_buy_desktop,
                                                    enable_quick_buy_mobile: block.settings.enable_quick_buy_mobile,
                                                    enable_quick_buy_qty_selector: block.settings.enable_quick_buy_qty_selector,
                                                    quick_buy_compact: block.settings.enable_quick_buy_compact,
                                                    enable_quick_buy_drawer: block.settings.enable_quick_buy_drawer,
                                                    layout: block.settings.layout,
                                                    show_image: block.settings.products_show_image,
                                                    show_vendor: block.settings.products_show_vendor,
                                                    show_title: block.settings.products_show_title,
                                                    show_rating: block.settings.products_show_rating,
                                                    show_price: block.settings.products_show_price,
                                                    show_stock: block.settings.products_show_stock
                                            -%}
                                        {%- endfor -%}
                                    {%- endif -%}
                                </ul>
                            {%- endif -%}
                        {%- endcapture -%}
                        {%- if block.settings.enable_tab -%}
                            {%- if block.settings.title != empty -%}
                                <div class="block-{{ block.id }} accordion-a compact{% if block.type == 'complementary_products' %} product-recommendations{% unless request.design_mode %} hidden{% endunless %}{% endif %}" {{ block_attributes }}>
                                    <details{% unless block.settings.collapse %} open{% endunless %} {{ block.shopify_attributes }}>
                                        <summary>
                                            {{ header_img }}<span>{{ block.settings.title }}</span>
                                        </summary>
                                        <div>
                                            {{ content }}
                                        </div>
                                    </details>
                                </div>
                            {%- endif -%}
                        {%- else -%}
                            <div class="block-{{ block.id }}{% if block.type == 'complementary_products' %} product-recommendations{% unless request.design_mode %} hidden{% endunless %}{% endif %}" {{ block_attributes }} {{ block.shopify_attributes }}>
                                {%- if block.settings.title != empty or header_img != empty -%}
                                  <{{ block.settings.title_size }} class="heading-has-image cols cols-mobile text-start align-middle">
                                    {% if header_img != empty %}<span>{{ header_img }}</span>{% endif %}
                                    <span>{{ block.settings.title }}</span>
                                  </{{ block.settings.title_size }}>
                                {% endif %}
                                {{ content }}
                            </div>
                        {%- endif -%}
                    {%- endif -%}
                {%- endcase -%}
            {%- endfor -%}
            {%- if custom_short_description and short_description != empty -%}
                <p class="mobile-only">{{ short_description }}</p>
            {%- endif -%}
        </div>
    </div>
    {%- if has_pros_and_cons == false and has_specs == false -%}
        {%- if section.settings.show_product_description and product.description != empty -%}
            <footer class="m6tb static desktop-hide">
                <nav class="hidden">
                    <ul>
                        <li class="active"><a href="#section-info">{{ 'product.description' | t }}</a></li>
                    </ul>
                </nav>
                <div>
                    <div id="section-info"{% if section.settings.product_description_mobile_collapse %} class="tab-closed"{% endif %}>
                        <{{ section.settings.description_title_size }} class="mobile-hide">{{ 'product.description' | t }}</{{ section.settings.description_title_size }}>
                        {%- if section.settings.product_description_enable_read_more -%}
                            <div class="m6lm">
                                {{ product.description }}
                            </div>
                            <p class="has-link-more"><a href="./" class="strong link-more">{{ 'general.read_more.read' | t }} <span>{{ 'general.read_more.more' | t }}</span> <span class="hidden">{{ 'general.read_more.less' | t }}</span> <i aria-hidden="true" class="icon-chevron-down"></i></a></p>
                        {%- else -%}
                            {{ product.description }}
                        {%- endif -%}
                    </div>
                </div>
            </footer>
        {%- endif %}
    {%- endif %}
</article>

{%- if has_pros_and_cons or has_specs -%}
    <div class="m6tb static">
        <nav class="hidden">
            <ul>
                <li class="active"><a href="#section-info">{{ 'product.description' | t }}</a></li>
            </ul>
        </nav>
        <div>
            <div id="section-info"{% if section.settings.product_description_mobile_collapse %} class="tab-closed"{% endif %}>
                <article class="cols b75">
                    {%- if section.settings.show_product_description -%}
                        <div class="w60 t55">
                            <{{ section.settings.description_title_size }} class="mobile-hide">{{ 'product.description' | t }}</{{ section.settings.description_title_size }}>
                            {%- if section.settings.product_description_enable_read_more -%}
                                <div class="m6lm">
                                    {{ product.description }}
                                </div>
                                <p class="has-link-more"><a href="./" class="strong link-more">{{ 'general.read_more.read' | t }} <span>{{ 'general.read_more.more' | t }}</span> <span class="hidden">{{ 'general.read_more.less' | t }}</span> <i aria-hidden="true" class="icon-chevron-down"></i></a></p>
                            {%- else -%}
                                {{ product.description }}
                            {%- endif -%}
                        </div>
                    {%- endif %}
                    <div class="w40 t45">
                        {%- if has_specs -%}
                            <{{ section.settings.specs_title_size }}>{{ 'product.specifications' | t }}</{{ section.settings.specs_title_size }}>
                            <ul class="l4if">
                                {%- for spec in specs -%}
                                    {%- assign spec_name = spec | split: ':' | first | strip -%}
                                    {%- assign spec_value = spec | split: ':' | last | strip -%}
                                    {%- if spec_name != empty and spec_value != empty -%}
                                        <li {% if specs.size > 9 and forloop.index > 9 %}class="hidden"{% endif %}>
                                            <span>{{ spec_name | remove: '<p>' | remove: '</p>' }}:</span> {{ spec_value | remove: '<p>' | remove: '</p>' }}
                                        </li>
                                    {%- endif -%}
                                {%- endfor -%}
                                {%- if specs.size > 9 -%}
                                    <li><a href="./" class="strong link-more">{{ 'general.read_more.read' | t }} <span>{{ 'general.read_more.more' | t }}</span> <span class="hidden">{{ 'general.read_more.less' | t }}</span> <i aria-hidden="true" class="icon-chevron-down"></i></a></li>
                                {%- endif -%}
                            </ul>
                        {%- endif %}
                        {%- if has_pros_and_cons -%}
                            <{{ section.settings.pros_cons_title_size }}>{{ 'product.pros_and_cons' | t }}</{{ section.settings.pros_cons_title_size }}>
                            <ul class="l4us static plus">
                                {%- for pro in pros -%}
                                    <li>{{ pro | remove: '<p>' | remove: '</p>' | strip }}</li>
                                {%- endfor -%}
                                {%- for con in cons -%}
                                    <li class="overlay-content">{{ con | remove: '<p>' | remove: '</p>' | strip }}</li>
                                {%- endfor -%}
                            </ul>
                        {%- endif %}
                    </div>
                </article>
            </div>
        </div>
    </div>
{%- endif %}

{% style %}
  @media only screen and (max-width: 760px) {
    #shopify-section-{{ section.id }} .m6tb .tabs-header, .m6tb.static .tabs-header, #shopify-section-{{ section.id }} .accordion-a.compact summary {
      font-size: var(--mob_{{ section.settings.mobile_tabs_title_size }});
    }
    .template-product .tabs-header, #quickshop {
      --main_h1: var(--mob_{{ section.settings.mobile_tabs_title_size }});
      --main_h2: var(--mob_{{ section.settings.mobile_tabs_title_size }});
      --main_h3: var(--mob_{{ section.settings.mobile_tabs_title_size }});
      --main_h4: var(--mob_{{ section.settings.mobile_tabs_title_size }});
      --main_h5: var(--mob_{{ section.settings.mobile_tabs_title_size }});
      --main_h6: var(--mob_{{ section.settings.mobile_tabs_title_size }});
    }
  }
{% endstyle %}

{% schema %}
{
  "name": "t:main.product.name",
  "class": "with-mobile-tab",
  "settings": [
    {
      "type": "header",
      "content": "t:main.product.settings.thumbs.header"
    },
    {
      "type": "checkbox",
      "id": "show_thumbs_desktop",
      "label": "t:main.product.settings.thumbs.show_thumbs_desktop.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_thumbs_mobile",
      "label": "t:main.product.settings.thumbs.show_thumbs_mobile.label",
      "default": true
    },
    {
      "type": "select",
      "id": "images_layout",
      "label": "t:main.product.settings.thumbs.images_layout.label",
      "options": [
        {
          "value": "aside",
          "label": "t:main.product.settings.thumbs.images_layout.options__1.label"
        },
        {
          "value": "bottom",
          "label": "t:main.product.settings.thumbs.images_layout.options__2.label"
        }
      ],
      "default": "bottom",
      "visible_if": "{{ section.settings.show_thumbs_desktop }}"
    },
    {
      "type": "header",
      "content": "t:main.product.settings.product_description.header"
    },
    {
      "id": "show_product_description",
      "type": "checkbox",
      "label": "t:main.product.settings.product_description.show_product_description.label",
      "default": true
    },
    {
      "type": "select",
      "id": "description_title_size",
      "label": "t:global.typography.title_size.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        },
        {
          "value": "h5",
          "label": "t:global.typography.title_size.h5.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "checkbox",
      "id": "product_description_enable_read_more",
      "label": "t:main.product.settings.product_description.product_description_enable_read_more.label",
      "visible_if": "{{ section.settings.show_product_description }}"
    },
    {
      "type": "header",
      "content": "t:main.product.settings.pros_and_cons.header"
    },
    {
      "type": "select",
      "id": "pros_cons_title_size",
      "label": "t:global.typography.title_size.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        },
        {
          "value": "h5",
          "label": "t:global.typography.title_size.h5.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "richtext",
      "id": "pros",
      "label": "t:main.product.settings.pros_and_cons.pros.label"
    },
    {
      "type": "richtext",
      "id": "cons",
      "label": "t:main.product.settings.pros_and_cons.cons.label"
    },
    {
      "type": "paragraph",
      "content": "t:main.product.settings.pros_and_cons.paragraph"
    },
    {
      "type": "header",
      "content": "t:main.product.settings.specifications.header"
    },
    {
      "type": "select",
      "id": "specs_title_size",
      "label": "t:global.typography.title_size.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        },
        {
          "value": "h5",
          "label": "t:global.typography.title_size.h5.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "richtext",
      "id": "specifications",
      "label": "t:main.product.settings.specifications.specifications.label"
    },
    {
      "type": "header",
      "content": "t:main.product.settings.mobile.header"
    },
    {
      "id": "product_description_mobile_collapse",
      "type": "checkbox",
      "label": "t:main.product.settings.mobile.product_description_mobile_collapse.label",
      "default": false
    },
    {
      "type": "select",
      "id": "mobile_tabs_title_size",
      "label": "t:main.product.settings.mobile.mobile_tabs_title_size.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        },
        {
          "value": "h5",
          "label": "t:global.typography.title_size.h5.label"
        }
      ],
      "default": "h4"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "upsell",
      "limit": 1,
      "name": "t:main.product.blocks.upsell.name",
      "settings": [
        {
          "type": "header",
          "content": "t:main.product.blocks.upsell.settings.heading_products"
        },
        {
          "type": "product_list",
          "id": "product_list",
          "label": "t:main.product.blocks.upsell.settings.product_list.label"
        },
        {
          "type": "range",
          "id": "visible_products",
          "min": 1,
          "max": 10,
          "step": 1,
          "label": "t:main.product.blocks.upsell.settings.visible_products.label",
          "default": 4
        },
        {
          "type": "checkbox",
          "id": "show_out_of_stock_products",
          "label": "t:main.product.blocks.upsell.settings.show_out_of_stock_products.label",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "variant_in_popup",
          "label": "t:main.product.blocks.upsell.settings.variant_in_popup.label",
          "info": "t:main.product.blocks.upsell.settings.variant_in_popup.info",
          "default": false
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.upsell.settings.heading_colors"
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:global.color_palette.label",
          "default": "scheme-1"
        },
        {
          "type": "select",
          "id": "variant_selector_color_scheme",
          "label": "t:main.product.blocks.upsell.settings.variant_selector_color_scheme.label",
          "options": [
            {
              "value": "primary",
              "label": "t:global.button.button_style.primary.label"
            },
            {
              "value": "secondary",
              "label": "t:global.button.button_style.secondary.label"
            },
            {
              "value": "tertiary",
              "label": "t:global.button.button_style.tertiary.label"
            },
            {
              "value": "buy_button",
              "label": "t:global.button.button_style.buy_button.label"
            },
            {
              "value": "dynamic_buy_button",
              "label": "t:global.button.button_style.dynamic_buy_button.label"
            }
          ],
          "default": "primary",
          "visible_if": "{{ block.settings.variant_in_popup }}"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.upsell.settings.header.label"
        },
        {
          "type": "select",
          "id": "heading_position",
          "label": "t:main.product.blocks.upsell.settings.heading_position.label",
          "options": [
            {
              "value": "left",
              "label": "t:main.product.blocks.upsell.settings.heading_position.options__left.label"
            },
            {
              "value": "center",
              "label": "t:main.product.blocks.upsell.settings.heading_position.options__center.label"
            }
          ],
          "default": "left"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "t:main.product.blocks.upsell.settings.heading_size.label",
          "options": [
            {
              "value": "p",
              "label": "t:main.product.blocks.upsell.settings.heading_size.options__small.label"
            },
            {
              "value": "h3",
              "label": "t:main.product.blocks.upsell.settings.heading_size.options__medium.label"
            },
            {
              "value": "h2",
              "label": "t:main.product.blocks.upsell.settings.heading_size.options__large.label"
            },
            {
              "value": "h1",
              "label": "t:main.product.blocks.upsell.settings.heading_size.options__extra_large.label"
            }
          ],
          "default": "p"
        },
        {
          "type": "inline_richtext",
          "id": "heading",
          "label": "t:main.product.blocks.upsell.settings.heading.label",
          "default": "You may be interested in these products"
        }
      ]
    },
    {
      "type": "features_cards",
      "name": "Features Cards",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Card 1"
        },
        {
          "type": "textarea",
          "id": "card1_svg",
          "label": "Card 1 SVG Code",
          "default": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\">\n  <path d=\"M24 24c-4.418 4.418-11.582 4.418-16 0S3.582 12.418 8 8s11.582-4.418 16 0\" stroke=\"#4A4A4A\" stroke-width=\"2.5\" stroke-linecap=\"round\"/>\n  <path d=\"m17.322 15.921-2.208-.61c-.723-.197-1.1-.66-1.1-1.073 0-.29.164-.576.479-.8s.76-.37 1.261-.37h.477c.864 0 1.53.422 1.697.894a.8.8 0 0 0 .756.526h.014a.82.82 0 0 0 .624-.288l.008-.01a.76.76 0 0 0 .129-.668l-.002-.009-.004-.008c-.351-1-1.401-1.732-2.655-1.906v-.427c0-.436-.377-.754-.798-.754h-.015c-.42 0-.798.318-.798.754v.426c-1.532.206-2.77 1.27-2.77 2.64 0 1.165.93 2.171 2.248 2.524l2.22.61c.707.196 1.087.66 1.087 1.073v.026a.96.96 0 0 1-.319.674 2.15 2.15 0 0 1-1.417.483h-.496c-.863 0-1.515-.422-1.684-.895v-.002a.81.81 0 0 0-.755-.51h-.014a.78.78 0 0 0-.628.293.73.73 0 0 0-.131.665l.003.011c.352 1.012 1.413 1.745 2.656 1.909v.398c0 .436.377.754.798.754H16c.21 0 .394-.079.546-.2l.007-.006.006-.006a.74.74 0 0 0 .24-.542v-.384a3.6 3.6 0 0 0 1.949-.875 2.4 2.4 0 0 0 .835-1.76v-.02c0-1.182-.949-2.172-2.26-2.537Z\" fill=\"#009EE0\" stroke=\"#009EE0\" stroke-width=\".5\"/>\n  <path d=\"M26.756 4.51a1.25 1.25 0 0 0-2.5 0zm-1.25 5.156v1.25c.69 0 1.25-.56 1.25-1.25zm-5.157-1.25a1.25 1.25 0 0 0 0 2.5zm3.907-3.906v5.156h2.5V4.51zm1.25 3.906H20.35v2.5h5.157z\" fill=\"#4A4A4A\"/>\n</svg>"
        },
        {
          "type": "text",
          "id": "card1_text",
          "label": "Card 1 Text",
          "default": "180-day money-back"
        },
        {
          "type": "header",
          "content": "Card 2"
        },
        {
          "type": "textarea",
          "id": "card2_svg",
          "label": "Card 2 SVG Code",
          "default": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\">\n  <path d=\"M30.77 16.647v6.478c0 .343-.142.673-.394.916s-.593.38-.95.38h-1.342c0 1.03-.424 2.018-1.18 2.747a4.1 4.1 0 0 1-2.848 1.139 4.1 4.1 0 0 1-2.848-1.139 3.82 3.82 0 0 1-1.18-2.748h-8.056c0 1.031-.424 2.02-1.18 2.748a4.1 4.1 0 0 1-2.848 1.139 4.1 4.1 0 0 1-2.848-1.139 3.82 3.82 0 0 1-1.18-2.748H2.573c-.356 0-.697-.136-.949-.38a1.27 1.27 0 0 1-.393-.915V7.578c0-1.03.424-2.02 1.18-2.748a4.1 4.1 0 0 1 2.848-1.14h12.084c1.068 0 2.093.41 2.848 1.139a3.82 3.82 0 0 1 1.18 2.748v2.591h2.685c.625 0 1.242.14 1.801.41a4 4 0 0 1 1.421 1.145l3.223 4.146q.058.085.094.18l.08.143q.09.219.094.454M9.286 24.42c0-.256-.079-.507-.227-.72a1.33 1.33 0 0 0-.602-.477 1.4 1.4 0 0 0-.776-.073c-.26.05-.5.173-.687.354a1.3 1.3 0 0 0-.368.663 1.25 1.25 0 0 0 .077.749c.101.237.273.439.494.581a1.38 1.38 0 0 0 1.696-.16c.251-.244.393-.573.393-.917m9.398-16.842c0-.344-.141-.673-.393-.916a1.37 1.37 0 0 0-.95-.38H5.26a1.37 1.37 0 0 0-.95.38 1.27 1.27 0 0 0-.393.916V21.83h1.047c.378-.4.838-.721 1.351-.94a4.15 4.15 0 0 1 3.26 0c.513.219.973.54 1.35.94h7.761zm2.686 7.773h5.37l-1.61-2.073a1.3 1.3 0 0 0-.474-.381 1.4 1.4 0 0 0-.601-.137H21.37zm4.028 9.07c0-.257-.08-.508-.227-.72a1.33 1.33 0 0 0-.602-.478 1.4 1.4 0 0 0-.776-.073c-.26.05-.5.173-.687.354a1.3 1.3 0 0 0-.368.663c-.052.252-.025.512.076.749s.274.439.495.581a1.38 1.38 0 0 0 1.695-.16c.252-.244.394-.573.394-.917m2.685-6.479H21.37v3.602a4.12 4.12 0 0 1 2.894-.983 4.1 4.1 0 0 1 2.772 1.268h1.047z\" fill=\"#4A4A4A\"/>\n  <path fill=\"#009EE0\" d=\"M5.333 8h11.333v5.333H5.333zm0 6h5.333v5.333H5.333zm6 0h5.333v5.333h-5.333z\"/>\n</svg>"
        },
        {
          "type": "text",
          "id": "card2_text",
          "label": "Card 2 Text",
          "default": "Free shipping"
        },
        {
          "type": "header",
          "content": "Card 3"
        },
        {
          "type": "textarea",
          "id": "card3_svg",
          "label": "Card 3 SVG Code",
          "default": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\">\n  <path d=\"m15.385 8.87-6.949.337a3.69 3.69 0 0 0-3.513 3.688v5.91A3.69 3.69 0 0 0 8.17 22.47l13.539 1.643a3.692 3.692 0 0 0 4.137-3.665V10.393\" stroke=\"#4A4A4A\" stroke-width=\"3.692\"/>\n  <path d=\"m10.154 15.472 2.877 3.556 9.154-10.413\" stroke=\"#009EE0\" stroke-width=\"3.692\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n</svg>"
        },
        {
          "type": "text",
          "id": "card3_text",
          "label": "Card 3 Text",
          "default": "FSA/HSA Eligible"
        }
      ]
    },
    {
      "type": "guarantee_banner",
      "name": "Guarantee Banner",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Guarantee Settings"
        },
        {
          "type": "text",
          "id": "guarantee_text",
          "label": "Guarantee Text",
          "default": "180-day money-back guarantee"
        },
        {
          "type": "header",
          "content": "Card Settings"
        },
        {
          "type": "text",
          "id": "card_title",
          "label": "Card Title",
          "default": "Use Your FSA/HSA Dollars Before You Lose Them"
        },
        {
          "type": "checkbox",
          "id": "show_learn_more",
          "label": "Show Learn More Button",
          "default": true
        },
        {
          "type": "text",
          "id": "learn_more_text",
          "label": "Learn More Button Text",
          "default": "Learn more"
        },
        {
          "type": "color",
          "id": "card_bg_color",
          "label": "Card Background Color",
          "default": "#1E306E"
        },
        {
          "type": "color",
          "id": "card_text_color",
          "label": "Card Text Color",
          "default": "#FFFFFF"
        },
        {
          "type": "header",
          "content": "Popup Settings"
        },
        {
          "type": "text",
          "id": "popup_title",
          "label": "Popup Title",
          "default": "FSA/HSA"
        },
        {
          "type": "textarea",
          "id": "popup_description",
          "label": "Popup Description",
          "default": "FSA (Flexible Spending Account) and HSA (Health Savings Account) are financial accounts where you are setting aside pre-tax money from your paycheck to pay for health expenses that are not covered by your insurance."
        },
        {
          "type": "text",
          "id": "popup_subtitle",
          "label": "Popup Subtitle",
          "default": "How can I buy with it?"
        },
        {
          "type": "richtext",
          "id": "popup_content",
          "label": "Popup Content",
          "default": "<p><strong>With an FSA Debit Card</strong> - If you have an FSA debit card, complete your purchase as you would with any other credit card.</p><p><strong>Without an FSA Debit Card</strong> - Simply complete your order and we'll send you an itemized receipt after your purchase. Get it here.</p>"
        },
        {
          "type": "color",
          "id": "overlay_color",
          "label": "Overlay Color",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "Overlay Opacity",
          "min": 10,
          "max": 90,
          "step": 5,
          "unit": "%",
          "default": 80
        }
      ]
    },
    {
      "type": "buy_button",
      "name": "t:main.product.blocks.buy_button.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "button_style",
          "label": "t:main.product.blocks.buy_button.settings.button_style.label",
          "options": [
            {
              "value": "plain",
              "label": "t:main.product.blocks.buy_button.settings.button_style.options__1.label"
            },
            {
              "value": "inv",
              "label": "t:main.product.blocks.buy_button.settings.button_style.options__2.label"
            }
          ],
          "default": "plain"
        },
        {
          "id": "show_amount_selection",
          "type": "checkbox",
          "label": "t:main.product.blocks.buy_button.settings.show_amount_selection.label",
          "default": true
        },
        {
          "id": "show_dynamic_buybutton",
          "type": "checkbox",
          "label": "t:main.product.blocks.buy_button.settings.show_dynamic_buybutton.label",
          "info": "t:main.product.blocks.buy_button.settings.show_dynamic_buybutton.info",
          "default": false
        },
        {
          "type": "select",
          "id": "button_style_dynamic_buybutton",
          "label": "t:main.product.blocks.buy_button.settings.button_style_dynamic_buybutton.label",
          "options": [
            {
              "value": "plain",
              "label": "t:main.product.blocks.buy_button.settings.button_style.options__1.label"
            },
            {
              "value": "inv",
              "label": "t:main.product.blocks.buy_button.settings.button_style.options__2.label"
            }
          ],
          "default": "plain"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.buy_button.settings.preorder.header"
        },
        {
          "id": "preorder",
          "type": "checkbox",
          "label": "t:main.product.blocks.buy_button.settings.preorder.preorder.label",
          "info": "t:main.product.blocks.buy_button.settings.preorder.preorder.info",
          "default": false
        },
        {
          "id": "preorder_button_text",
          "type": "text",
          "label": "t:main.product.blocks.buy_button.settings.preorder.preorder_button_text.label",
          "default": "Pre-order",
          "visible_if": "{{ block.settings.preorder }}"
        },
        {
          "id": "preorder_label",
          "type": "richtext",
          "label": "t:main.product.blocks.buy_button.settings.preorder.preorder_label.label",
          "info": "t:main.product.blocks.buy_button.settings.preorder.preorder_label.info",
          "visible_if": "{{ block.settings.preorder }}"
        },
        {
          "type": "select",
          "id": "preorder_label_color_palette",
          "label": "t:main.product.blocks.buy_button.settings.preorder.preorder_label_color_palette.label",
          "options": [
            {
              "value": "primary",
              "label": "t:global.button.button_style.primary.label"
            },
            {
              "value": "secondary",
              "label": "t:global.button.button_style.secondary.label"
            },
            {
              "value": "tertiary",
              "label": "t:global.button.button_style.tertiary.label"
            },
            {
              "value": "buy_button",
              "label": "t:global.button.button_style.buy_button.label"
            },
            {
              "value": "dynamic_buy_button",
              "label": "t:global.button.button_style.dynamic_buy_button.label"
            }
          ],
          "default": "secondary",
          "visible_if": "{{ block.settings.preorder }}"
        },
        {
          "id": "preorder_show_dynamic_buybutton",
          "type": "checkbox",
          "label": "t:main.product.blocks.buy_button.settings.preorder.preorder_show_dynamic_buybutton.label",
          "default": false,
          "visible_if": "{{ block.settings.preorder }}"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.buy_button.settings.gift_card.header"
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "default": false,
          "label": "t:main.product.blocks.buy_button.settings.gift_card.show_gift_card_recipient.label",
          "info": "t:main.product.blocks.buy_button.settings.gift_card.show_gift_card_recipient.info"
        }
      ]
    },
    {
      "type": "complementary_products",
      "name": "t:main.product.blocks.complementary_products.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.complementary_products.settings.paragraph"
        },
        {
          "id": "enable_tab",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.enable_tab.label",
          "default": true
        },
        {
          "id": "collapse",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.collapse.label"
        },
        {
          "id": "title",
          "type": "text",
          "label": "t:main.product.blocks.complementary_products.settings.title.label",
          "default": "Pairs well with"
        },
        {
          "id": "icon",
          "type": "select",
          "label": "t:main.product.blocks.complementary_products.settings.icon.label",
          "info": "t:main.product.blocks.complementary_products.settings.icon.info",
          "options": [
            {
              "value": "none",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__1.label"
            },
            {
              "value": "group",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__2.label"
            },
            {
              "value": "notification",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__3.label"
            },
            {
              "value": "cloud_data",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__4.label"
            },
            {
              "value": "verified",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__5.label"
            },
            {
              "value": "truck",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__6.label"
            },
            {
              "value": "image_placeholder",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__7.label"
            },
            {
              "value": "help_call",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__8.label"
            },
            {
              "value": "filters",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__9.label"
            },
            {
              "value": "shopping_bag",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__10.label"
            },
            {
              "value": "global_shipping",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__11.label"
            },
            {
              "value": "barcode",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__12.label"
            },
            {
              "value": "delivery_box_1",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__13.label"
            },
            {
              "value": "delivery_box_2",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__14.label"
            },
            {
              "value": "statistic",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__15.label"
            },
            {
              "value": "review",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__16.label"
            },
            {
              "value": "email",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__17.label"
            },
            {
              "value": "coin",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__18.label"
            },
            {
              "value": "24_hour_clock",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__19.label"
            },
            {
              "value": "question",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__20.label"
            },
            {
              "value": "24_7_call",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__21.label"
            },
            {
              "value": "speech_bubbles",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__22.label"
            },
            {
              "value": "coupon",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__23.label"
            },
            {
              "value": "mobile_payment",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__24.label"
            },
            {
              "value": "calculator",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__25.label"
            },
            {
              "value": "secure",
              "label": "t:main.product.blocks.complementary_products.settings.icon.options__26.label"
            }
          ],
          "default": "none"
        },
        {
          "id": "header_image",
          "type": "image_picker",
          "label": "t:main.product.blocks.complementary_products.settings.header_image.label",
          "info": "t:main.product.blocks.complementary_products.settings.header_image.info"
        },
        {
          "id": "header_image_svg",
          "type": "url",
          "label": "t:main.product.blocks.complementary_products.settings.header_image_svg.label",
          "info": "t:main.product.blocks.complementary_products.settings.header_image_svg.info"
        },
        {
          "id": "header_image_width",
          "type": "range",
          "label": "t:main.product.blocks.complementary_products.settings.header_image_width.label",
          "min": 20,
          "max": 150,
          "step": 5,
          "unit": "px",
          "default": 25
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.complementary_products.settings.products.header"
        },
        {
          "type": "select",
          "id": "layout",
          "label": "t:main.product.blocks.complementary_products.settings.products.layout.label",
          "options": [
            {
              "value": "grid",
              "label": "t:main.product.blocks.complementary_products.settings.products.layout.options__1.label"
            },
            {
              "value": "list",
              "label": "t:main.product.blocks.complementary_products.settings.products.layout.options__2.label"
            }
          ],
          "default": "grid"
        },
        {
          "id": "number_of_items",
          "type": "range",
          "label": "t:main.product.blocks.complementary_products.settings.products.number_of_items.label",
          "min": 1,
          "max": 5,
          "step": 1,
          "default": 3,
          "visible_if": "{{ block.settings.layout == 'grid' }}"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.complementary_products.settings.quick_buy.header"
        },
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.complementary_products.settings.quick_buy.paragraph"
        },
        {
          "id": "enable_quick_buy_desktop",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_quick_buy_desktop.label",
          "default": true
        },
        {
          "id": "enable_quick_buy_mobile",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_quick_buy_mobile.label",
          "default": true
        },
        {
          "id": "enable_quick_buy_drawer",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_quick_buy_drawer.label",
          "info": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_quick_buy_drawer.info",
          "default": true
        },
        {
          "id": "enable_quick_buy_qty_selector",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_quick_buy_qty_selector.label",
          "info": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_quick_buy_qty_selector.info",
          "default": true
        },
        {
          "id": "enable_color_picker",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_color_picker.label"
        },
        {
          "id": "enable_quick_buy_compact",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_quick_buy_compact.label",
          "info": "t:main.product.blocks.complementary_products.settings.quick_buy.enable_quick_buy_compact.info",
          "default": true
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.complementary_products.settings.products_layout.header"
        },
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.complementary_products.settings.products_layout.paragraph"
        },
        {
          "id": "products_show_image",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.products_layout.products_show_image.label",
          "default": true
        },
        {
          "id": "products_show_labels",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.products_layout.products_show_labels.label"
        },
        {
          "id": "products_show_vendor",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.products_layout.products_show_vendor.label"
        },
        {
          "id": "products_show_title",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.products_layout.products_show_title.label",
          "default": true
        },
        {
          "id": "products_show_rating",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.products_layout.products_show_rating.label"
        },
        {
          "id": "products_show_price",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.products_layout.products_show_price.label",
          "default": true
        },
        {
          "id": "products_show_stock",
          "type": "checkbox",
          "label": "t:main.product.blocks.complementary_products.settings.products_layout.products_show_stock.label"
        }
      ]
    },
    {
      "type": "content",
      "name": "t:main.product.blocks.content.name",
      "settings": [
        {
          "id": "enable_tab",
          "type": "checkbox",
          "label": "t:main.product.blocks.content.settings.enable_tab.label",
          "default": true
        },
        {
          "id": "collapse",
          "type": "checkbox",
          "label": "t:main.product.blocks.content.settings.collapse.label",
          "default": true,
          "visible_if": "{{ block.settings.enable_tab }}"
        },
        {
          "type": "select",
          "id": "title_size",
          "label": "t:global.typography.title_size.label",
          "info": "t:main.product.blocks.content.settings.title_size.info",
          "options": [
            {
              "value": "h1",
              "label": "t:global.typography.title_size.h1.label"
            },
            {
              "value": "h2",
              "label": "t:global.typography.title_size.h2.label"
            },
            {
              "value": "h3",
              "label": "t:global.typography.title_size.h3.label"
            },
            {
              "value": "h4",
              "label": "t:global.typography.title_size.h4.label"
            },
            {
              "value": "h5",
              "label": "t:global.typography.title_size.h5.label"
            }
          ],
          "default": "h2"
        },
        {
          "id": "title",
          "type": "text",
          "label": "t:main.product.blocks.content.settings.title.label"
        },
        {
          "id": "icon",
          "type": "select",
          "label": "t:main.product.blocks.content.settings.icon.label",
          "info": "t:main.product.blocks.content.settings.icon.info",
          "options": [
            {
              "value": "none",
              "label": "t:main.product.blocks.content.settings.icon.options__1.label"
            },
            {
              "value": "group",
              "label": "t:main.product.blocks.content.settings.icon.options__2.label"
            },
            {
              "value": "notification",
              "label": "t:main.product.blocks.content.settings.icon.options__3.label"
            },
            {
              "value": "cloud_data",
              "label": "t:main.product.blocks.content.settings.icon.options__4.label"
            },
            {
              "value": "verified",
              "label": "t:main.product.blocks.content.settings.icon.options__5.label"
            },
            {
              "value": "truck",
              "label": "t:main.product.blocks.content.settings.icon.options__6.label"
            },
            {
              "value": "image_placeholder",
              "label": "t:main.product.blocks.content.settings.icon.options__7.label"
            },
            {
              "value": "help_call",
              "label": "t:main.product.blocks.content.settings.icon.options__8.label"
            },
            {
              "value": "filters",
              "label": "t:main.product.blocks.content.settings.icon.options__9.label"
            },
            {
              "value": "shopping_bag",
              "label": "t:main.product.blocks.content.settings.icon.options__10.label"
            },
            {
              "value": "global_shipping",
              "label": "t:main.product.blocks.content.settings.icon.options__11.label"
            },
            {
              "value": "barcode",
              "label": "t:main.product.blocks.content.settings.icon.options__12.label"
            },
            {
              "value": "delivery_box_1",
              "label": "t:main.product.blocks.content.settings.icon.options__13.label"
            },
            {
              "value": "delivery_box_2",
              "label": "t:main.product.blocks.content.settings.icon.options__14.label"
            },
            {
              "value": "statistic",
              "label": "t:main.product.blocks.content.settings.icon.options__15.label"
            },
            {
              "value": "review",
              "label": "t:main.product.blocks.content.settings.icon.options__16.label"
            },
            {
              "value": "email",
              "label": "t:main.product.blocks.content.settings.icon.options__17.label"
            },
            {
              "value": "coin",
              "label": "t:main.product.blocks.content.settings.icon.options__18.label"
            },
            {
              "value": "24_hour_clock",
              "label": "t:main.product.blocks.content.settings.icon.options__19.label"
            },
            {
              "value": "question",
              "label": "t:main.product.blocks.content.settings.icon.options__20.label"
            },
            {
              "value": "24_7_call",
              "label": "t:main.product.blocks.content.settings.icon.options__21.label"
            },
            {
              "value": "speech_bubbles",
              "label": "t:main.product.blocks.content.settings.icon.options__22.label"
            },
            {
              "value": "coupon",
              "label": "t:main.product.blocks.content.settings.icon.options__23.label"
            },
            {
              "value": "mobile_payment",
              "label": "t:main.product.blocks.content.settings.icon.options__24.label"
            },
            {
              "value": "calculator",
              "label": "t:main.product.blocks.content.settings.icon.options__25.label"
            },
            {
              "value": "secure",
              "label": "t:main.product.blocks.content.settings.icon.options__26.label"
            }
          ],
          "default": "none"
        },
        {
          "id": "header_image",
          "type": "image_picker",
          "label": "t:main.product.blocks.content.settings.header_image.label",
          "info": "t:main.product.blocks.content.settings.header_image.info"
        },
        {
          "id": "header_image_svg",
          "type": "url",
          "label": "t:main.product.blocks.content.settings.header_image_svg.label",
          "info": "t:main.product.blocks.content.settings.header_image_svg.info"
        },
        {
          "id": "header_image_width",
          "type": "range",
          "label": "t:main.product.blocks.content.settings.header_image_width.label",
          "min": 20,
          "max": 150,
          "step": 5,
          "unit": "px",
          "default": 25
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.content.settings.content.header"
        },
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.content.settings.content.paragraph"
        },
        {
          "id": "text",
          "type": "richtext",
          "label": "t:main.product.blocks.content.settings.content.text.label"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:main.product.blocks.content.settings.content.page.label"
        },
        {
          "id": "image",
          "type": "image_picker",
          "label": "t:main.product.blocks.content.settings.content.image.label"
        },
        {
          "id": "image_width",
          "type": "range",
          "label": "t:main.product.blocks.content.settings.content.image_width.label",
          "min": 10,
          "max": 465,
          "step": 5,
          "unit": "px",
          "default": 465
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.content.settings.custom_code.header"
        },
        {
          "id": "liquid",
          "type": "liquid",
          "label": "t:main.product.blocks.content.settings.custom_code.liquid.label"
        },
        {
          "id": "html",
          "type": "html",
          "label": "t:main.product.blocks.content.settings.custom_code.html.label"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.content.settings.form.header"
        },
        {
          "id": "show_contact_form",
          "type": "checkbox",
          "label": "t:main.product.blocks.content.settings.form.show_contact_form.label",
          "info": "t:main.product.blocks.content.settings.form.show_contact_form.info"
        }
      ]
    },
    {
      "type": "custom_html",
      "name": "t:main.product.blocks.custom_html_snippet.name",
      "settings": [
        {
          "type": "html",
          "id": "custom_html",
          "label": "t:main.product.blocks.custom_html_snippet.settings.custom_html_snippet.label",
          "info": "t:main.product.blocks.custom_html_snippet.settings.custom_html_snippet.info"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:main.product.blocks.custom_liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:main.product.blocks.custom_liquid.settings.custom_liquid.label",
          "info": "t:main.product.blocks.custom_liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "usp",
      "name": "t:main.product.blocks.usp.name",
      "settings": [
        {
          "id": "usp",
          "type": "richtext",
          "label": "t:main.product.blocks.usp.settings.usp.label",
          "default": "<p>Tell a unique detail about this product</p>"
        }
      ]
    },
    {
      "type": "inventory",
      "name": "t:main.product.blocks.inventory.name",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.inventory.settings.paragraph"
        }
      ]
    },
    {
      "type": "price",
      "name": "t:main.product.blocks.price.name",
      "limit": 1,
      "settings": [
        {
          "id": "show_tax",
          "type": "checkbox",
          "label": "t:main.product.blocks.price.settings.show_tax.label",
          "default": false
        }
      ]
    },
    {
      "type": "codes",
      "name": "t:main.product.blocks.codes.name",
      "limit": 1,
      "settings": [
        {
          "id": "show_sku",
          "type": "checkbox",
          "label": "t:main.product.blocks.codes.settings.show_sku.label",
          "default": true
        },
        {
          "id": "show_barcode",
          "type": "checkbox",
          "label": "t:main.product.blocks.codes.settings.show_barcode.label",
          "default": true
        }
      ]
    },
    {
      "type": "quantity_rules",
      "name": "t:main.product.blocks.quantity_rules.name",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.quantity_rules.settings.paragraph"
        },
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.quantity_rules.settings.paragraph-2"
        },
        {
          "id": "text",
          "type": "inline_richtext",
          "label": "t:global.typography.text.header"
        },
        {
          "type": "color_scheme",
          "id": "color_palette",
          "label": "t:global.color_palette.label",
          "default": "scheme-3"
        }
      ]
    },
    {
      "type": "shipping_timer",
      "name": "t:main.product.blocks.shipping_timer.name",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.shipping_timer.settings.paragraph"
        }
      ]
    },
    {
      "type": "short_description",
      "name": "t:main.product.blocks.short_description.name",
      "settings": [
        {
          "type": "checkbox",
          "id": "show_read_more",
          "label": "t:main.product.blocks.short_description.settings.show_read_more.label",
          "default": true
        },
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.short_description.settings.paragraph"
        }
      ]
    },
    {
      "type": "spacer",
      "name": "t:main.product.blocks.spacer.name",
      "settings": [
        {
          "id": "height",
          "type": "range",
          "label": "t:main.product.blocks.spacer.settings.height.label",
          "min": 0,
          "max": 200,
          "step": 5,
          "unit": "px",
          "default": 20
        }
      ]
    },
    {
      "type": "urgency",
      "name": "t:main.product.blocks.urgency.name",
      "settings": [
        {
          "type": "number",
          "id": "min_stock",
          "label": "t:main.product.blocks.urgency.settings.stock.min_stock.label",
          "info": "t:main.product.blocks.urgency.settings.stock.min_stock.info",
          "default": 5
        },
        {
          "type": "text",
          "id": "min_stock_meta",
          "label": "t:main.product.blocks.urgency.settings.stock.min_stock_meta.label",
          "info": "t:main.product.blocks.urgency.settings.stock.min_stock_meta.info"
        },
        {
          "type": "checkbox",
          "id": "show_level",
          "label": "t:main.product.blocks.urgency.settings.stock.show_level.label",
          "info": "t:main.product.blocks.urgency.settings.stock.show_level.info",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_bar_unavailable",
          "label": "t:main.product.blocks.urgency.settings.stock.show_bar_unavailable.label",
          "info": "t:main.product.blocks.urgency.settings.stock.show_bar_unavailable.info",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_bar_available_nostock",
          "label": "t:main.product.blocks.urgency.settings.stock.show_bar_available_nostock.label",
          "info": "t:main.product.blocks.urgency.settings.stock.show_bar_available_nostock.info",
          "default": true
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.urgency.settings.layout.header"
        },
        {
          "type": "select",
          "id": "layout",
          "label": "t:main.product.blocks.urgency.settings.layout.layout.label",
          "options": [
            {
              "value": "small",
              "label": "t:main.product.blocks.urgency.settings.layout.layout.small.label"
            },
            {
              "value": "medium",
              "label": "t:main.product.blocks.urgency.settings.layout.layout.medium.label"
            }
          ],
          "default": "small"
        },
        {
          "type": "select",
          "id": "position_stock_message",
          "label": "t:main.product.blocks.urgency.settings.layout.position_stock_message.label",
          "options": [
            {
              "value": "above",
              "label": "t:main.product.blocks.urgency.settings.layout.position_stock_message.above.label"
            },
            {
              "value": "below",
              "label": "t:main.product.blocks.urgency.settings.layout.position_stock_message.below.label"
            }
          ],
          "default": "below"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.urgency.settings.colors.header"
        },
        {
          "type": "color_scheme",
          "id": "color_palette",
          "label": "t:main.product.blocks.urgency.settings.colors.color_palette.label",
          "default": "scheme-1"
        },
        {
          "type": "color",
          "id": "color_progressbar",
          "label": "t:main.product.blocks.urgency.settings.colors.color_progressbar.label",
          "info": "t:main.product.blocks.urgency.settings.colors.color_progressbar.info",
          "default": "#121234"
        },
        {
          "type": "color_background",
          "id": "gradient_progressbar",
          "label": "t:main.product.blocks.urgency.settings.colors.gradient_progressbar.label",
          "info": "t:main.product.blocks.urgency.settings.colors.gradient_progressbar.info",
          "default": "linear-gradient(98.88deg, #E84A93 44.11%, #FBC34A 93.24%)"
        },
        {
          "type": "color",
          "id": "background_progress_bar",
          "label": "t:main.product.blocks.urgency.settings.colors.background_progress_bar.label",
          "info": "t:main.product.blocks.urgency.settings.colors.background_progress_bar.info",
          "default": "#F0F0F0"
        },
        {
          "type": "color",
          "id": "tooltip_background",
          "label": "t:main.product.blocks.urgency.settings.colors.tooltip_background.label",
          "default": "#192733"
        },
        {
          "type": "color",
          "id": "tooltip_text_color",
          "label": "t:main.product.blocks.urgency.settings.colors.tooltip_text_color.label",
          "default": "#ffffff"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.urgency.settings.content.header"
        },
        {
          "type": "inline_richtext",
          "id": "message",
          "label": "t:main.product.blocks.urgency.settings.content.message.label",
          "info": "t:main.product.blocks.urgency.settings.content.message.info",
          "default": "Be quick! Only <b>*inventory*</b> products left in stock!"
        },
        {
          "type": "inline_richtext",
          "id": "outofstock_message",
          "label": "t:main.product.blocks.urgency.settings.content.outofstock_message.label",
          "default": "Unfortunately, this product is out of stock"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:main.product.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "label": "t:main.product.blocks.title.settings.layout.label",
          "info": "t:main.product.blocks.title.settings.layout.info",
          "options": [
            {
              "value": "title-top",
              "label": "t:main.product.blocks.title.settings.layout.options__1.label"
            },
            {
              "value": "title-right",
              "label": "t:main.product.blocks.title.settings.layout.options__2.label"
            }
          ],
          "default": "title-top"
        },
        {
          "type": "select",
          "id": "title_size",
          "label": "t:global.typography.title_size.label",
          "options": [
            {
              "value": "h1",
              "label": "t:global.typography.title_size.h1.label"
            },
            {
              "value": "h2",
              "label": "t:global.typography.title_size.h2.label"
            },
            {
              "value": "h3",
              "label": "t:global.typography.title_size.h3.label"
            },
            {
              "value": "h4",
              "label": "t:global.typography.title_size.h4.label"
            },
            {
              "value": "h5",
              "label": "t:global.typography.title_size.h5.label"
            }
          ],
          "default": "h2"
        },
        {
          "id": "title",
          "type": "inline_richtext",
          "label": "t:main.product.blocks.title.settings.title.label",
          "info": "t:main.product.blocks.title.settings.title.info"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.title.settings.vendor.header"
        },
        {
          "id": "show_vendor_brand",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.vendor.show_vendor_brand.label",
          "default": true
        },
        {
          "id": "show_vendor_name",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.vendor.show_vendor_name.label",
          "default": true
        },
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.title.settings.vendor.paragraph"
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.title.settings.product_rating.header"
        },
        {
          "id": "show_product_rating",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.product_rating.show_product_rating.label",
          "info": "t:main.product.blocks.title.settings.product_rating.show_product_rating.info",
          "default": true
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.title.settings.share_buttons.header"
        },
        {
          "id": "share_whatsapp",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.share_buttons.share_whatsapp.label",
          "default": true
        },
        {
          "id": "share_facebook",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.share_buttons.share_facebook.label",
          "default": true
        },
        {
          "id": "share_twitter",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.share_buttons.share_twitter.label",
          "default": true
        },
        {
          "id": "share_pinterest",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.share_buttons.share_pinterest.label",
          "default": true
        },
        {
          "id": "share_messenger",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.share_buttons.share_messenger.label",
          "default": true
        },
        {
          "id": "share_email",
          "type": "checkbox",
          "label": "t:main.product.blocks.title.settings.share_buttons.share_email.label",
          "default": true
        },
        {
          "type": "header",
          "content": "t:main.product.blocks.title.settings.mobile.header"
        },
        {
          "type": "select",
          "id": "layout_mobile",
          "label": "t:main.product.blocks.title.settings.mobile.layout_mobile.label",
          "info": "t:main.product.blocks.title.settings.mobile.layout_mobile.info",
          "options": [
            {
              "value": "title-top",
              "label": "t:main.product.blocks.title.settings.mobile.layout_mobile.options__1.label"
            },
            {
              "value": "title-bottom",
              "label": "t:main.product.blocks.title.settings.mobile.layout_mobile.options__2.label"
            }
          ],
          "default": "title-top"
        },
        {
          "type": "header",
          "content": "Custom Price Settings"
        },
        {
          "id": "price_suffix",
          "type": "text",
          "label": "Price suffix text",
          "info": "Add custom text after the price (e.g., 'with JULY code')",
          "default": "(with FLASH70 code)"
        }
      ]
    },
    {
      "type": "trustbadge",
      "name": "t:main.product.blocks.trustbadge.name",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.trustbadge.settings.paragraph"
        }
      ]
    },
    {
      "type": "custom_color_selection",
      "name": "Custom Color Selection",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "color_label",
          "label": "Color Label",
          "default": "Color",
          "info": "Text that appears before the color name (e.g., 'Color: blue')"
        }
      ]
    },
    {
      "type": "promo_banner",
      "name": "Promo Banner",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "promo_title",
          "label": "Promo Title",
          "default": "BACK TO SCHOOL SALE"
        },
        {
          "type": "text",
          "id": "promo_subtitle",
          "label": "Promo Subtitle",
          "default": "UP TO 70% OFF EVERYTHING"
        },
        {
          "type": "text",
          "id": "promo_additional",
          "label": "Additional Text",
          "default": "+ free shipping"
        },
        {
          "type": "color",
          "id": "promo_bg_color",
          "label": "Background Color",
          "default": "#f8f9fa"
        },
        {
          "type": "color",
          "id": "promo_text_color",
          "label": "Text Color",
          "default": "#6c757d"
        },
        {
          "type": "select",
          "id": "promo_icon",
          "label": "Icon",
          "options": [
            {
              "value": "tag",
              "label": "Tag"
            },
            {
              "value": "percent",
              "label": "Percent"
            },
            {
              "value": "gift",
              "label": "Gift"
            },
            {
              "value": "none",
              "label": "No Icon"
            }
          ],
          "default": "tag"
        },
        {
          "type": "url",
          "id": "promo_link",
          "label": "Promo Link (optional)"
        }
      ]
    },
    {
      "type": "variant_selection",
      "name": "t:main.product.blocks.variant_selection.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "selection_type",
          "label": "t:main.product.blocks.variant_selection.settings.selection_type.label",
          "options": [
            {
              "value": "dropdown",
              "label": "t:main.product.blocks.variant_selection.settings.selection_type.options__1.label"
            },
            {
              "value": "buttons",
              "label": "t:main.product.blocks.variant_selection.settings.selection_type.options__2.label"
            }
          ],
          "default": "dropdown"
        },
        {
          "type": "checkbox",
          "id": "options",
          "label": "t:main.product.blocks.variant_selection.settings.options.label"
        },
        {
          "type": "checkbox",
          "id": "show_variant_images",
          "label": "t:main.product.blocks.variant_selection.settings.show_variant_images.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_unavailable_variants",
          "label": "t:main.product.blocks.variant_selection.settings.show_unavailable_variants.label",
          "info": "t:main.product.blocks.variant_selection.settings.show_unavailable_variants.info",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_single_options",
          "label": "t:main.product.blocks.variant_selection.settings.show_single_options.label",
          "visible_if": "{{ block.settings.options }}"
        },
        {
          "type": "checkbox",
          "id": "enable_selling_plans",
          "label": "t:main.product.blocks.variant_selection.settings.enable_selling_plans.label",
          "info": "t:main.product.blocks.variant_selection.settings.enable_selling_plans.info",
          "default": true
        }
      ]
    },
    {
      "type": "volume_pricing",
      "name": "t:main.product.blocks.volume_pricing.name",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.volume_pricing.settings.paragraph"
        },
        {
          "type": "paragraph",
          "content": "t:main.product.blocks.volume_pricing.settings.paragraph-2"
        },
        {
          "id": "text",
          "type": "inline_richtext",
          "label": "t:global.typography.text.header",
          "default": "<b>Volume pricing</b>"
        },
        {
          "type": "color_scheme",
          "id": "color_palette",
          "label": "t:global.color_palette.label",
          "default": "scheme-3"
        }
      ]
    }
  ]
}
{% endschema %}

<style>
  /* Custom styles for breadcrumbs and title in right panel */

  .product-breadcrumbs {
    margin-bottom: 10px;
  }

  .product-breadcrumbs ol {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
    color: rgba(74, 74, 74, 0.71);
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: capitalize;
  }

  .product-breadcrumbs ol li {
    display: flex;
    align-items: center;
  }

  .product-breadcrumbs ol li:not(:last-child)::after {
    content: '/';
    margin: 0 8px;
    color: #ccc;
  }

  .product-breadcrumbs ol li a {
    color: #4a4a4a !important;
    opacity: 1 !important;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .product-breadcrumbs ol li a:hover {
    color: #000000 !important;
    text-decoration: none;
  }

  .product-breadcrumbs ol li:last-child {
    color: rgba(74, 74, 74, 0.71);
    font-weight: 400;
  }

  .product-right-title {
    margin: 0 0 15px 0 !important;
    color: #4A4A4A;
    font-family: Lato, sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .product-right-price {
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .price-original {
    color: rgba(104, 104, 104, 0.51);
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-decoration-line: line-through;
  }

  .price-sale {
    color: #F52A43;
    font-family: Lato, sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  .price-regular {
    color: #000000;
    font-family: Lato, sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .price-suffix {
    color: rgba(104, 104, 104, 0.71);
    font-family: Lato, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .product-right-meta {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 14px;
  }

  .product-right-meta li {
    display: flex;
    align-items: center;
  }

  .product-right-meta a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .product-right-meta a:hover {
    color: #00AEF8;
    text-decoration: none;
  }

  .product-right-meta .strong {
    font-weight: 600;
    margin-right: 5px;
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .product-right-header {
      margin-bottom: 15px;
      padding-bottom: 12px;
    }

    .product-breadcrumbs ol {
      font-size: 14px;
    }

    .product-breadcrumbs ol li:not(:last-child)::after {
      margin: 0 6px;
    }

    .product-right-title {
      font-size: 22px;
    }

    .product-right-meta {
      gap: 10px;
      font-size: 13px;
    }

    .price-original {
      font-size: 15px;
    }

    .price-sale {
      font-size: 17px;
    }

    .price-regular {
      font-size: 17px;
    }

    .price-suffix {
      font-size: 13px;
    }
  }

  @media (max-width: 480px) {
    .product-breadcrumbs ol {
      font-size: 13px;
    }

    .product-breadcrumbs ol li:not(:last-child)::after {
      margin: 0 4px;
    }

    .product-right-title {
      font-size: 20px;
    }

    .product-right-meta {
      gap: 8px;
      font-size: 12px;
      flex-direction: column;
      align-items: flex-start;
    }

    .price-original {
      font-size: 14px;
    }

    .price-sale {
      font-size: 16px;
    }

    .price-regular {
      font-size: 16px;
    }

    .price-suffix {
      font-size: 12px;
    }

    .product-right-price {
      gap: 6px;
    }
  }

  /* Custom Color Selection Styles */
  .custom-color-selection {
    margin: 20px 0;
    padding: 0;
  }

  .custom-color-wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .custom-color-label {
    color: #4A4A4A !important;
    font-family: Lato, sans-serif !important;
    font-size: 16px !important;
    font-style: normal !important;
    font-weight: 400 !important;
    line-height: normal !important;
    margin: 0 !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    display: flex !important;
    align-items: center !important;
  }

  .custom-color-selected {
    color: #4A4A4A !important;
    font-weight: 500 !important;
    text-transform: lowercase !important;
  }

  .custom-color-options {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 12px !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .custom-color-option {
    position: relative !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .custom-color-input {
    position: absolute !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important;
    outline: none !important;
    box-shadow: none !important;
    transform: none !important;
    animation: none !important;
  }

  .custom-color-input:before,
  .custom-color-input:after {
    display: none !important;
    content: none !important;
  }

  .custom-color-swatch {
    display: block !important;
    cursor: pointer !important;
    margin: 0 !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    transform: none !important;
    animation: none !important;
    transition: transform 0.2s ease !important;
  }

  .custom-color-swatch:before,
  .custom-color-swatch:after {
    display: none !important;
    content: none !important;
  }

  .custom-color-circle {
    display: block !important;
    width: 26px !important;
    height: 26px !important;
    border-radius: 50% !important;
    border: 2px solid #E5E5E5 !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    transition: all 0.2s ease !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    transform: none !important;
    animation: none !important;
  }

  .custom-color-circle:before,
  .custom-color-circle:after {
    display: none !important;
    content: none !important;
  }

  .custom-color-input:checked + .custom-color-swatch .custom-color-circle {
    border-color: #4A4A4A !important;
    box-shadow: 0 0 0 2px rgba(74, 74, 74, 0.2) !important;
    transform: scale(1.1) !important;
  }

  .custom-color-input:disabled + .custom-color-swatch {
    cursor: not-allowed !important;
    opacity: 0.5 !important;
  }

  .custom-color-input:disabled + .custom-color-swatch .custom-color-circle {
    opacity: 0.5 !important;
  }

  .custom-color-swatch:hover .custom-color-circle {
    transform: scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }

  .custom-color-input:checked + .custom-color-swatch:hover .custom-color-circle {
    transform: scale(1.1) !important;
  }

  .custom-color-input:disabled + .custom-color-swatch:hover .custom-color-circle {
    transform: none !important;
    box-shadow: none !important;
  }

  /* Tooltip styles for disabled colors */
  .custom-color-swatch[data-tooltip] {
    position: relative !important;
  }

  .custom-color-swatch[data-tooltip]:hover::before {
    content: attr(data-tooltip) !important;
    position: absolute !important;
    bottom: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    white-space: nowrap !important;
    z-index: 1000 !important;
    margin-bottom: 5px !important;
    pointer-events: none !important;
    opacity: 1 !important;
    display: block !important;
  }

  .custom-color-swatch[data-tooltip]:hover::after {
    content: '' !important;
    position: absolute !important;
    bottom: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    border: 4px solid transparent !important;
    border-top-color: rgba(0, 0, 0, 0.8) !important;
    z-index: 1000 !important;
    margin-bottom: 1px !important;
    pointer-events: none !important;
    display: block !important;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .custom-color-selection {
      margin: 16px 0;
    }

    .custom-color-wrapper {
      gap: 10px;
    }

    .custom-color-label {
      font-size: 14px !important;
    }

    .custom-color-options {
      gap: 10px !important;
    }

    .custom-color-circle {
      width: 24px !important;
      height: 24px !important;
    }
  }

  @media (max-width: 480px) {
    .custom-color-selection {
      margin: 12px 0;
    }

    .custom-color-wrapper {
      gap: 8px;
    }

    .custom-color-options {
      gap: 8px !important;
    }

    .custom-color-circle {
      width: 22px !important;
      height: 22px !important;
    }
  }

  /* Promo Banner Styles */
  .promo-banner-block {
    margin: 20px 0;
    padding: 0;
  }

  .promo-banner-container {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    text-decoration: none;
    color: #4a4a4a !important;
  }

  .promo-banner-link {
    display: block;
    text-decoration: none;
    color: inherit;
    width: 100%;
  }

  .promo-banner-link:hover {
    text-decoration: none;
    color: inherit;
  }

  .promo-banner-content {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
  }

  .promo-banner-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    opacity: 0.8;
  }

  .promo-banner-icon svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
  }

  .promo-banner-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .promo-banner-title {
    font-family: Lato, sans-serif;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
  }

  .promo-banner-subtitle {
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.2;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.3px;
     margin-bottom: 4px;
  }

  .promo-banner-additional {
    font-family: Lato, sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.2;
    margin: 0;
    opacity: 0.8;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .promo-banner-container {
      padding: 14px 16px;
    }

    .promo-banner-content {
      gap: 10px;
    }

    .promo-banner-title {
      font-size: 12px;
    }

    .promo-banner-subtitle {
      font-size: 14px;
    }

    .promo-banner-additional {
      font-size: 11px;
    }

    .promo-banner-icon {
      width: 18px;
      height: 18px;
    }

    .promo-banner-icon svg {
      width: 18px;
      height: 18px;
    }
  }

  @media (max-width: 480px) {
    .promo-banner-block {
      margin: 16px 0;
    }

    .promo-banner-container {
      padding: 12px 14px;
    }

    .promo-banner-content {
      gap: 8px;
    }

    .promo-banner-title {
      font-size: 11px;
    }

    .promo-banner-subtitle {
      font-size: 13px;
    }

    .promo-banner-additional {
      font-size: 10px;
    }

    .promo-banner-icon {
      width: 16px;
      height: 16px;
    }

    .promo-banner-icon svg {
      width: 16px;
      height: 16px;
    }
  }

  /* Features Cards Styles */
  .features-cards-block {
    margin: 20px 0;
    padding: 0;
  }

  .features-cards-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    background-color: #f6f6f6;
    padding: 20px;
    border-radius: 8px;
  }

  .feature-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
    flex: 1;
    max-width: 150px;
  }

  .feature-card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
  }

  .feature-card-icon svg {
    width: 32px;
    height: 32px;
    display: block;
  }

  .feature-card-text {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    line-height: 1.3;
  }



  /* Guarantee Banner Styles */
  .guarantee-banner-block {
    margin: 5px 0;
  }

  .guarantee-text {
    color: #6c757d;
    font-family: Lato, sans-serif;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    margin: 0 0 16px 0;
    padding: 0;
    line-height: 1.4;
  }

  .guarantee-card {
    width: 100%;
    max-width: 1098px;
    height: 130px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 13.299px;
    background: linear-gradient(180deg, #1E306E 0%, #0575E6 100%);
    box-sizing: border-box;
    position: relative;
    margin: 0 auto;
  }

  .guarantee-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding-top: 10px;
  }

  .guarantee-card-title {
    color: #FFF;
    text-align: center;
    font-family: Lato, sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 900;
    line-height: 135%;
    margin: 0;
    max-width: 734.751px;
    padding: 0;
    border: none;
    background: none;
  }

  .guarantee-learn-more {
    background: none !important;
    border: none !important;
    color: #FFF !important;
    font-family: Lato, sans-serif !important;
    font-size: 13px !important;
    font-weight: 400 !important;
    text-decoration: underline !important;
    cursor: pointer !important;
    padding: 0 !important;
    margin: 0 !important;
    transition: opacity 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    box-shadow: none !important;
    outline: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    animation: none !important;
    transform: none !important;
    min-height: 30px !important;
  }

  .guarantee-learn-more:before,
  .guarantee-learn-more:after {
    display: none !important;
    content: none !important;
  }

  .guarantee-learn-more:hover,
  .guarantee-learn-more:focus,
  .guarantee-learn-more:active {
    opacity: 0.8 !important;
    background: none !important;
    color: #FFF !important;
    box-shadow: none !important;
    outline: none !important;
    animation: none !important;
    transform: none !important;
  }

  .guarantee-learn-more:hover:before,
  .guarantee-learn-more:hover:after,
  .guarantee-learn-more:focus:before,
  .guarantee-learn-more:focus:after,
  .guarantee-learn-more:active:before,
  .guarantee-learn-more:active:after {
    display: none !important;
    content: none !important;
  }

  .guarantee-learn-more svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  /* Guarantee Popup Styles */
  .guarantee-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    padding: 20px;
    box-sizing: border-box;
  }

  .guarantee-popup-overlay.active {
    display: flex;
  }

  .guarantee-popup-content {
    background: #FFF;
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .guarantee-popup-close {
    position: absolute !important;
    top: 20px !important;
    right: 20px !important;
    background: none !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
    cursor: pointer !important;
    transition: opacity 0.3s ease !important;
    animation: none !important;
    transform: none !important;
    box-shadow: none !important;
    outline: none !important;
  }

  .guarantee-popup-close:before,
  .guarantee-popup-close:after {
    display: none !important;
    content: none !important;
  }

  .guarantee-popup-close:hover,
  .guarantee-popup-close:focus {
    opacity: 0.7 !important;
    background: none !important;
    box-shadow: none !important;
    outline: none !important;
    animation: none !important;
    transform: none !important;
  }

  .guarantee-popup-close:hover:before,
  .guarantee-popup-close:hover:after,
  .guarantee-popup-close:focus:before,
  .guarantee-popup-close:focus:after {
    display: none !important;
    content: none !important;
  }

  .guarantee-popup-title {
    color: #333;
    font-family: Lato, sans-serif;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 20px 0;
    line-height: 1.3;
  }

  .guarantee-popup-description {
    color: #898989;
    font-size: 15px;
    line-height: normal;
    margin: 0 0 24px 0;
    font-family: "Open Sans", sans-serif;
  }

  .guarantee-popup-subtitle {
    color: #333;
    font-family: Lato, sans-serif;
    font-size: 20px;
    font-weight: 600;
    margin: 24px 0 16px 0;
    line-height: 1.3;
  }

  .guarantee-popup-text {
    color: #898989;
    font-size: 15px;
    line-height: normal;
    margin-top: 24px;
    font-family: "Open Sans", sans-serif;
  }

  .guarantee-popup-text p {
    margin: 0 0 16px 0;
  }

  .guarantee-popup-text p:last-child {
    margin-bottom: 0;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .features-cards-container {
      gap: 20px;
      padding: 15px;
    }

    .feature-card {
      max-width: 120px;
    }

    .feature-card-text {
      font-size: 13px;
    }

    .guarantee-banner-block {
      margin: 5px 0;
    }

    .guarantee-text {
      font-size: 13px;
      margin: 0 0 12px 0;
    }

    .guarantee-card {
      height: auto;
      min-height: 100px;
      padding: 24px 16px;
    }

    .guarantee-card-title {
      font-size: 20px;
      line-height: 130%;
    }

    .guarantee-learn-more {
      font-size: 14px !important;
    }

    .guarantee-popup-content {
      padding: 30px 20px;
      margin: 20px;
      max-height: 85vh;
    }



    .guarantee-popup-close {
      top: 15px !important;
      right: 15px !important;
    }

    .guarantee-popup-title {
      font-size: 24px;
    }

    .guarantee-popup-description,
    .guarantee-popup-text {
      font-size: 14px;
    }

    .guarantee-popup-subtitle {
      font-size: 18px;
    }
  }

  @media (max-width: 480px) {
    .features-cards-container {
      gap: 15px;
      padding: 15px;
    }

    .feature-card {
      max-width: none;
      width: 100%;
    }

    .feature-card-text {
      font-size: 12px;
    }

    .guarantee-banner-block {
      margin: 5px 0;
    }

    .guarantee-text {
      font-size: 12px;
    }

    .guarantee-card {
      padding: 20px 12px;
    }

    .guarantee-card-title {
      font-size: 18px;
    }

    .guarantee-popup-content {
      padding: 24px 16px;
      margin: 16px;
    }

    .guarantee-popup-close {
      top: 12px !important;
      right: 12px !important;
    }

    .guarantee-popup-title {
      font-size: 22px;
    }
  }
</style>

<script>
  // Update custom price when variant changes
  document.addEventListener('DOMContentLoaded', function() {
    const productForm = document.querySelector('#product-form-{{ section.id }}');
    const priceContainer = document.querySelector('#product-right-price-{{ section.id }}');

    if (productForm && priceContainer) {
      // Listen for variant changes
      productForm.addEventListener('change', function(e) {
        if (e.target.name === 'id' || e.target.name.includes('options[')) {
          updateCustomPrice();
        }
      });

      // Function to update price display
      function updateCustomPrice() {
        const variantId = productForm.querySelector('[name="id"]').value;
        const productData = JSON.parse(document.querySelector('#ProductVariantsJSON-{{ product.id }}').textContent || '{}');

        // Find current variant
        const currentVariant = productData.variants?.find(variant => variant.id == variantId);

        if (currentVariant) {
          const priceOriginal = priceContainer.querySelector('[data-compare-price]');
          const priceSale = priceContainer.querySelector('.price-sale[data-price]');
          const priceRegular = priceContainer.querySelector('.price-regular[data-price]');
          const priceSuffix = priceContainer.querySelector('.price-suffix');

          // Clear existing price display
          priceContainer.innerHTML = '';

          if (currentVariant.compare_at_price > currentVariant.price) {
            // Has discount - show original price, SALE price, and suffix
            const originalSpan = document.createElement('span');
            originalSpan.className = 'price-original';
            originalSpan.setAttribute('data-compare-price', '');
            originalSpan.textContent = '$' + (currentVariant.compare_at_price / 100).toFixed(2);
            priceContainer.appendChild(originalSpan);

            const saleSpan = document.createElement('span');
            saleSpan.className = 'price-sale';
            saleSpan.setAttribute('data-price', '');
            saleSpan.textContent = 'SALE $' + (currentVariant.price / 100).toFixed(2);
            priceContainer.appendChild(saleSpan);

            // Add suffix if it exists
            const suffixText = '{{ title_block.settings.price_suffix }}';
            if (suffixText && suffixText.trim() !== '') {
              const suffixSpan = document.createElement('span');
              suffixSpan.className = 'price-suffix';
              suffixSpan.textContent = suffixText;
              priceContainer.appendChild(suffixSpan);
            }
          } else {
            // No discount - show regular price only
            const regularSpan = document.createElement('span');
            regularSpan.className = 'price-regular';
            regularSpan.setAttribute('data-price', '');
            regularSpan.textContent = '$' + (currentVariant.price / 100).toFixed(2);
            priceContainer.appendChild(regularSpan);
          }
        }
      }
    }

    // Custom Color Selection Handler
    const customColorInputs = document.querySelectorAll('.custom-color-input');
    const selectedColorSpan = document.querySelector('.custom-color-selected[data-selected-color]');

    // Prevent multiple rapid clicks
    let isProcessing = false;

    customColorInputs.forEach(input => {
      input.addEventListener('change', function(e) {
        if (!this.checked || isProcessing) return;

        isProcessing = true;

        try {
          // Update selected color text
          if (selectedColorSpan && this.dataset.colorName) {
            selectedColorSpan.textContent = this.dataset.colorName;
          }

          // Get variant ID first - this is the most important part
          const variantId = this.dataset.variantId;

          if (variantId) {
            // Find the product form - make sure we're updating the right one
            const productForm = document.querySelector('#product-form-{{ section.id }}, form[action*="/cart/add"]');

            // Method 1: Try to click the corresponding main variant radio button
            const mainVariantRadio = productForm ?
              productForm.querySelector(`input[name="variant-id"][value="${variantId}"], input[name="id"][value="${variantId}"]`) :
              document.querySelector(`input[name="variant-id"][value="${variantId}"], input[name="id"][value="${variantId}"]`);

            if (mainVariantRadio && !mainVariantRadio.checked) {
              // Temporarily remove our listener to prevent recursion
              this.removeEventListener('change', arguments.callee);

              // Click the main variant radio - this will handle everything
              mainVariantRadio.click();

              // Re-add our listener after a short delay
              setTimeout(() => {
                this.addEventListener('change', arguments.callee);
              }, 100);

            } else {
              // Method 2: Try select dropdown
              const mainVariantSelect = productForm ?
                productForm.querySelector('[name="variant-id"], [name="id"]') :
                document.querySelector('[name="variant-id"], [name="id"]');

              if (mainVariantSelect && mainVariantSelect.tagName === 'SELECT') {
                mainVariantSelect.value = variantId;
                mainVariantSelect.dispatchEvent(new Event('change', { bubbles: true }));
              }

              // Method 3: Update hidden input if exists
              const hiddenVariantInput = productForm ?
                productForm.querySelector('input[name="id"][type="hidden"]') :
                document.querySelector('input[name="id"][type="hidden"]');

              if (hiddenVariantInput) {
                hiddenVariantInput.value = variantId;
                hiddenVariantInput.dispatchEvent(new Event('change', { bubbles: true }));
              }

              // Method 4: Handle image slider switching separately if needed
              const l4prIndex = this.dataset.l4prIndex;
              if (l4prIndex !== undefined && l4prIndex !== null) {
                setTimeout(() => {
                  if (typeof window.l4prClick === 'function') {
                    window.l4prClick(this);
                  }
                }, 50);
              }
            }
          }

        } catch (error) {
          console.warn('Custom color selection error:', error);
        } finally {
          // Reset processing flag after a delay
          setTimeout(() => {
            isProcessing = false;
          }, 200);
        }
      });
    });

    // Universal Guarantee Banner Popup Functions
    window.openGuaranteePopup = function(popupId) {
      const overlay = document.getElementById('guarantee-popup-overlay-' + popupId);
      if (overlay) {
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
      }
    };

    window.closeGuaranteePopup = function(popupId) {
      const overlay = document.getElementById('guarantee-popup-overlay-' + popupId);
      if (overlay) {
        overlay.classList.remove('active');
        document.body.style.overflow = '';
      }
    };

    // Close popup on Escape key for all guarantee popups
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        const activeOverlays = document.querySelectorAll('.guarantee-popup-overlay.active');
        activeOverlays.forEach(function(overlay) {
          overlay.classList.remove('active');
          document.body.style.overflow = '';
        });
      }
    });

    // Close popup when clicking on overlay background
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('guarantee-popup-overlay')) {
        e.target.classList.remove('active');
        document.body.style.overflow = '';
      }
    });


  });
</script>
