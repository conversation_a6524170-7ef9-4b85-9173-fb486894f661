<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Variant Test</title>
    <link rel="stylesheet" href="assets/screen.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 40px;
        }
        .test-section h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .current-style {
            margin-bottom: 30px;
        }
        .new-style {
            margin-bottom: 30px;
        }
        .label {
            font-weight: bold;
            margin-bottom: 10px;
            display: block;
        }
        
        /* Current style simulation */
        .current-variant-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .current-variant-buttons button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: #f8f8f8;
            border-radius: 4px;
            cursor: pointer;
        }
        .current-variant-buttons button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Color Variant Selection Test</h1>
        
        <div class="test-section">
            <h3>Current Style (Text Buttons)</h3>
            <div class="current-style">
                <span class="label">Color</span>
                <div class="current-variant-buttons">
                    <button class="active">Black</button>
                    <button>Blue</button>
                    <button>Orange</button>
                    <button>Beige</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>New Style (Color Circles)</h3>
            <div class="new-style">
                <span class="label">Color: <span style="color: #0066cc;">blue</span></span>
                <div class="f8pr-variant-selection no-zindex">
                    <ul class="check box f8pr-custom-colors">
                        <li>
                            <input type="radio" id="option-black" name="color-test" value="black" title="Black">
                            <label for="option-black"></label>
                        </li>
                        <li>
                            <input type="radio" id="option-blue" name="color-test" value="blue" title="Blue" checked>
                            <label for="option-blue"></label>
                        </li>
                        <li>
                            <input type="radio" id="option-orange" name="color-test" value="orange" title="Orange">
                            <label for="option-orange"></label>
                        </li>
                        <li>
                            <input type="radio" id="option-beige" name="color-test" value="beige" title="Beige">
                            <label for="option-beige"></label>
                        </li>
                        <li>
                            <input type="radio" id="option-white" name="color-test" value="white" title="White">
                            <label for="option-white"></label>
                        </li>
                        <li>
                            <input type="radio" id="option-disabled" name="color-test" value="disabled" title="Disabled Color" disabled>
                            <label for="option-disabled"></label>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize custom color variants
        function initCustomColorVariants() {
            const variantSelections = document.querySelectorAll('.check.f8pr-custom-colors');
            
            variantSelections.forEach(function(checkBox) {
                const labels = checkBox.querySelectorAll('label');
                
                labels.forEach(function(label) {
                    const input = label.previousElementSibling;
                    if (input && input.type === 'radio') {
                        const variantTitle = input.title || '';
                        let color = '#ccc'; // default color
                        
                        // Extract color from variant title (assuming color is the last word)
                        const colorName = variantTitle.split(' ').pop().toLowerCase();
                        
                        // Define color mapping
                        const colorMap = {
                            'black': '#000000',
                            'blue': '#0066cc',
                            'orange': '#ff6600',
                            'beige': '#d4c4a8',
                            'white': '#ffffff',
                            'red': '#cc0000',
                            'green': '#00cc00',
                            'yellow': '#ffcc00',
                            'purple': '#6600cc',
                            'pink': '#ff66cc',
                            'brown': '#8b4513',
                            'gray': '#808080',
                            'grey': '#808080',
                            'navy': '#000080',
                            'maroon': '#800000',
                            'olive': '#808000',
                            'lime': '#00ff00',
                            'aqua': '#00ffff',
                            'teal': '#008080',
                            'silver': '#c0c0c0',
                            'fuchsia': '#ff00ff',
                            'disabled': '#cccccc'
                        };
                        
                        color = colorMap[colorName] || colorName;
                        
                        // Set CSS custom property for the color
                        label.style.setProperty('--variant-color', color);
                    }
                });
            });
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initCustomColorVariants);
        
        // Add click handlers for demo
        document.addEventListener('DOMContentLoaded', function() {
            const colorInputs = document.querySelectorAll('input[name="color-test"]');
            const colorLabel = document.querySelector('.new-style .label span');
            
            colorInputs.forEach(function(input) {
                input.addEventListener('change', function() {
                    if (this.checked) {
                        const colorName = this.title.toLowerCase();
                        colorLabel.textContent = colorName;
                        colorLabel.style.color = getComputedStyle(this.nextElementSibling).getPropertyValue('--variant-color');
                    }
                });
            });
        });
    </script>
</body>
</html>
